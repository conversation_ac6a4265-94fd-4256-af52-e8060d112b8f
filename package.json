{"name": "project-k", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node ./node_modules/typeorm/cli", "typeorm:run-migrations": "npm run typeorm migration:run -- -d ./ormconfig.ts", "typeorm:generate-migration": "npm run typeorm -- -d ./ormconfig.ts migration:generate ./src/database/migrations/$npm_config_name", "typeorm:create-migration": "npm run typeorm -- migration:create src/database/migrations/$npm_config_name", "typeorm:create-migration-windows": "npm run typeorm -- migration:create src/database/migrations/%npm_config_name%", "typeorm:revert-migration": "npm run typeorm -- -d ./ormconfig.ts migration:revert", "seed": "node dist/src/database/seeders/config/seederconfig", "seed:refresh": "node dist/src/database/seeders/config/seederconfig --refresh", "make:seeder": "node src/database/seeders/config/make-seeder.seeder.ts"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/platform-express": "^10.0.0", "@nestjs/typeorm": "^10.0.2", "@types/multer": "^1.4.12", "aws-sdk": "^2.1692.0", "bcrypt": "^5.1.1", "cashfree-pg": "^4.3.6", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "date-fns": "^4.1.0", "exceljs": "^4.4.0", "firebase-admin": "^13.4.0", "moment": "^2.30.1", "mysql": "^2.18.1", "mysql2": "^3.12.0", "nestjs-paginate": "^9.3.0", "otpauth": "^9.3.6", "pg": "^8.13.1", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "typeorm": "^0.3.20"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "nestjs-seeder": "^0.3.2", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}