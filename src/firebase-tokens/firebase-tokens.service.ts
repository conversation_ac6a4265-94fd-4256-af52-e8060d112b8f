import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, In } from "typeorm";
import { FirebaseToken } from "./entities/firebase-token.entity";
import { CreateOrUpdateFirebaseTokenDto } from "./dto/create-or-update-firebase-token.dto";

@Injectable()
export class FirebaseTokensService {
  constructor(
    @InjectRepository(FirebaseToken)
    private readonly firebaseTokenRepository: Repository<FirebaseToken>,
  ) {}

  async createOrUpdateToken(
    createOrUpdateFirebaseTokenDto: CreateOrUpdateFirebaseTokenDto,
  ): Promise<FirebaseToken> {
    const { user_id, token } = createOrUpdateFirebaseTokenDto;

    // Check if a token already exists for the user
    let firebaseToken = await this.firebaseTokenRepository.findOne({
      where: { user_id },
    });

    if (firebaseToken) {
      // Update the existing token
      firebaseToken.token = token;
    } else {
      // Create a new token
      firebaseToken = this.firebaseTokenRepository.create({
        user_id,
        token,
      });
    }

    return this.firebaseTokenRepository.save(firebaseToken);
  }

  async getTokens(userIds: string[]): Promise<string[]> {
    const firebaseTokens = await this.firebaseTokenRepository.find({
      where: { user_id: In(userIds) }, // Corrected the condition to filter by user_id
    });

    // Extract tokens into an array
    return firebaseTokens.map((token) => token.token);
  }
}
