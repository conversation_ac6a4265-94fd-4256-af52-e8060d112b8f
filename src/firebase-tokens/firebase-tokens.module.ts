import { Modu<PERSON> } from "@nestjs/common";
import { FirebaseTokensService } from "./firebase-tokens.service";
import { FirebaseTokensController } from "./firebase-tokens.controller";
import { TypeOrmModule } from "@nestjs/typeorm";
import { FirebaseToken } from "./entities/firebase-token.entity";

@Module({
  imports: [TypeOrmModule.forFeature([FirebaseToken])],
  controllers: [FirebaseTokensController],
  providers: [FirebaseTokensService],
  exports: [FirebaseTokensService],
})
export class FirebaseTokensModule {}
