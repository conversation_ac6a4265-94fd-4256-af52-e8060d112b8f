import { Controller, Post, Body } from "@nestjs/common";
import { FirebaseTokensService } from "./firebase-tokens.service";
import { CreateOrUpdateFirebaseTokenDto } from "./dto/create-or-update-firebase-token.dto";

@Controller("firebase-tokens")
export class FirebaseTokensController {
  constructor(private readonly firebaseTokensService: FirebaseTokensService) {}

  @Post("create-or-update")
  async createOrUpdateToken(
    @Body() createOrUpdateFirebaseTokenDto: CreateOrUpdateFirebaseTokenDto,
  ) {
    const token = await this.firebaseTokensService.createOrUpdateToken(
      createOrUpdateFirebaseTokenDto,
    );
    return {
      status: true,
      message: "Firebase token created or updated successfully",
      data: token,
    };
  }
}
