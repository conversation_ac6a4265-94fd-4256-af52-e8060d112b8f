import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Seeder } from "nestjs-seeder";
import { District } from "src/districts/entities/districts.entity";
import { Repository } from "typeorm";

@Injectable()
export class DistrictSeeder implements Seeder {
  constructor(
    @InjectRepository(District)
    private districtRepository: Repository<District>,
  ) {}

  async seed(): Promise<any> {
    // List of districts to be inserted
    const districts = [
      { name: "Alappuzha", slug: "alappuzha" },
      { name: "Ernakulam", slug: "ernakulam" },
      { name: "<PERSON><PERSON><PERSON><PERSON>", slug: "idukki" },
      { name: "Kannur", slug: "kannur" },
      { name: "Kasaragod", slug: "kasaragod" },
      { name: "Kollam", slug: "kollam" },
      { name: "Kottayam", slug: "kottayam" },
      { name: "Kozhikode", slug: "kozhikode" },
      { name: "Malappuram", slug: "malappuram" },
      { name: "Palakkad", slug: "palakkad" },
      { name: "Pathanamthitta", slug: "pathanamthitta" },
      { name: "Thiruvananthapuram", slug: "thiruvananthapuram" },
      { name: "Thrissur", slug: "thrissur" },
      { name: "Wayanad", slug: "wayanad" },
    ];

    // Loop through and insert districts if they don't already exist
    const districtPromises = districts.map(async (district) => {
      const existingDistrict = await this.districtRepository.findOneBy({
        name: district.name,
      });
      if (!existingDistrict) {
        await this.districtRepository.save(district);
        console.log(`District '${district.name}' created.`);
      }
    });

    await Promise.all(districtPromises);
  }

  async drop(): Promise<any> {
    // Clear all districts from the table
    return await this.districtRepository.clear();
  }
}
