import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddAutoBookingEmabledKey1749377252484
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "hospitals",
      new TableColumn({
        name: "auto_booking_enabled",
        type: "boolean",
        isNullable: true,
        default: false,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("hospitals", "auto_booking_enabled");
  }
}
