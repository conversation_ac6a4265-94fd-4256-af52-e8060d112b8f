import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddExcerciseType1746473373151 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "bookings",
      new TableColumn({
        name: "type",
        type: "enum",
        enum: ["consultation", "service", "fast_tag"],
        default: "'consultation'",
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("bookings", "type");
  }
}
