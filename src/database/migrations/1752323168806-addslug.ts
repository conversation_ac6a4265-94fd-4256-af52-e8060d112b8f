import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class Addslug1752323168806 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "doctors",
      new TableColumn({
        name: "slug",
        type: "varchar",
        isNullable: true,
      }),
    );

    await queryRunner.addColumn(
      "hospitals",
      new TableColumn({
        name: "slug",
        type: "varchar",
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("doctors", "slug");
    await queryRunner.dropColumn("hospitals", "slug");
  }
}
