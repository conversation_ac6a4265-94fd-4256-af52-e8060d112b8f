import {
  MigrationInterface,
  QueryRunner,
  TableColumn,
  TableForeign<PERSON>ey,
} from "typeorm";

export class AddserviceId1746476812865 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "bookings",
      new TableColumn({
        name: "hospital_service_id",
        type: "varchar",
        length: "36", // UUID as varchar in MySQL
        isNullable: true,
      }),
    );

    await queryRunner.createForeignKey(
      "bookings",
      new TableForeignKey({
        columnNames: ["hospital_service_id"],
        referencedTableName: "hospital_services",
        referencedColumnNames: ["id"],
        onDelete: "CASCADE",
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable("bookings");
    const foreignKeyUser = table?.foreignKeys.find(
      (fk) => fk.columnNames.indexOf("hospital_service_id") !== -1,
    );
    if (foreignKeyUser) {
      await queryRunner.dropForeignKey("bookings", foreignKeyUser);
    }
    await queryRunner.dropColumn("bookings", "hospital_service_id");
  }
}
