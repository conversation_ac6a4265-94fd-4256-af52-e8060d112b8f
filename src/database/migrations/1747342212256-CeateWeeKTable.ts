import { MigrationInterface, QueryRunner, Table } from "typeorm";

export class CeateWeeKTable1747342212256 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: "weeks",
        columns: [
          {
            name: "id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isPrimary: true,
            isGenerated: false,
          },
          {
            name: "name",
            type: "varchar",
            isNullable: true,
          },
          {
            name: "order",
            type: "int",
            isNullable: true,
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable("weeks");
  }
}
