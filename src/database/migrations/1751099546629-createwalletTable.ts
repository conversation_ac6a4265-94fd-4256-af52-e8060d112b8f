import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from "typeorm";

export class CreatewalletTable1751099546629 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: "wallet",
        columns: [
          {
            name: "id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isPrimary: true,
            isGenerated: false, // MySQL doesn't support automatic UUID generation natively
          },
          {
            name: "hospital_id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isNullable: true,
          },
          {
            name: "doctor_id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isNullable: true,
          },
          {
            name: "total_amount",
            type: "decimal",
            precision: 10,
            scale: 2,
            default: 0,
            isNullable: true,
          },
          {
            name: "requested_amount",
            type: "decimal",
            precision: 10,
            scale: 2,
            default: 0,
            isNullable: true,
          },
          {
            name: "balance_amount",
            type: "decimal",
            precision: 10,
            scale: 2,
            default: 0,
            isNullable: true,
          },
          {
            name: "withdrawal_amount",
            type: "decimal",
            precision: 10,
            scale: 2,
            default: 0,
            isNullable: true,
          },
        ],
      }),
      true,
    );

    await queryRunner.createForeignKey(
      "wallet",
      new TableForeignKey({
        columnNames: ["hospital_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "hospitals",
        onDelete: "CASCADE",
      }),
    );
    await queryRunner.createForeignKey(
      "wallet",
      new TableForeignKey({
        columnNames: ["doctor_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "doctors",
        onDelete: "CASCADE",
      }),
    );
  }
  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable("wallet");
    const foreignKeys = table.foreignKeys;

    await Promise.all(
      foreignKeys.map((foreignKey) =>
        queryRunner.dropForeignKey("wallet", foreignKey),
      ),
    );

    await queryRunner.dropTable("wallet");
  }
}
