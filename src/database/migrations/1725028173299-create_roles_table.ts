import { MigrationInterface, QueryRunner, Table } from "typeorm";

export class CreateRolesTable1725028173299 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: "roles",
        columns: [
          {
            name: "id",
            type: "varchar",
            length: "36", // UUID stored as a 36-character string
            isPrimary: true,
            isGenerated: false, // MySQL doesn't support native UUID generation
          },
          {
            name: "name",
            type: "varchar",
            isUnique: true,
          },
          {
            name: "pages",
            type: "json", // MySQL supports JSON natively
            isNullable: true,
          },
          {
            name: "created_at",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
          },
          {
            name: "updated_at",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
            onUpdate: "CURRENT_TIMESTAMP",
          },
          {
            name: "deleted_at",
            type: "timestamp",
            isNullable: true,
          },
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable("roles");
  }
}
