import { MigrationInterface, QueryRunner, Table } from "typeorm";

export class CreateHospitalTables1730390821117 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: "hospitals",
        columns: [
          {
            name: "id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isPrimary: true,
            isGenerated: false, // MySQL doesn't support automatic UUID generation natively
          },
          {
            name: "name",
            type: "varchar",
            isNullable: false,
          },
          {
            name: "logo",
            type: "varchar",
            isNullable: false,
          },
          {
            name: "location",
            type: "varchar",
            isNullable: false,
          },
          {
            name: "address",
            type: "json", // MySQL supports JSON natively
            isNullable: false,
          },
          {
            name: "billing_address",
            type: "json",
            isNullable: false,
          },
          {
            name: "gst",
            type: "varchar",
            isNullable: false,
          },
          {
            name: "website",
            type: "varchar",
            isNullable: true,
          },
          {
            name: "ratings",
            type: "json",
            isNullable: true,
          },
          {
            name: "feedbacks",
            type: "json",
            isNullable: true,
          },
          {
            name: "parent_id",
            type: "varchar",
            length: "36", // UUID stored as varchar in MySQL
            isNullable: true,
          },
          {
            name: "documents",
            type: "json",
            isNullable: true,
          },
          {
            name: "fastTag",
            type: "json",
            isNullable: true,
          },
          {
            name: "departments",
            type: "json",
            isNullable: true,
          },
          {
            name: "current",
            type: "varchar",
            isNullable: false,
            default: "'pending'",
          },
          {
            name: "isDisabled",
            type: "boolean",
            isNullable: false,
            default: false,
          },
          {
            name: "isDeactivated",
            type: "boolean",
            isNullable: false,
            default: false,
          },
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable("hospitals");
  }
}
