import { MigrationInterface, QueryRunner, Table, TableColumn } from "typeorm";

export class DocumentsTable1747259664655 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("doctors", "documents");
    await queryRunner.dropColumn("hospitals", "documents");

    await queryRunner.createTable(
      new Table({
        name: "documents",
        columns: [
          {
            name: "id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isPrimary: true,
            isGenerated: false,
          },
          {
            name: "doctor_id",
            type: "varchar",
            scale: 36,
            isNullable: true,
          },
          {
            name: "hospital_id",
            type: "varchar",
            scale: 36,
            isNullable: true,
          },
          {
            name: "name",
            type: "varchar",
            isNullable: true,
          },
          {
            name: "file",
            type: "varchar",
            isNullable: true,
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "hospitals",
      new TableColumn({
        name: "documents",
        type: "varchar",
        isNullable: true,
      }),
    );
    await queryRunner.addColumn(
      "doctors",
      new TableColumn({
        name: "documents",
        type: "varchar",
        isNullable: true,
      }),
    );
    await queryRunner.dropTable("documents");
  }
}
