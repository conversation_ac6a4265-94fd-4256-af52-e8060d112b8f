import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddPaymentTypeColumn1746752901234 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add payment_type to bookings table
    await queryRunner.addColumn(
      "bookings",
      new TableColumn({
        name: "payment_type",
        type: "varchar",
        length: "255",
        default: "'online'", // String values need quotes
        isNullable: true,
      }),
    );

    // Add payment_type to transactions table
    await queryRunner.addColumn(
      "transactions",
      new TableColumn({
        name: "payment_type",
        type: "varchar",
        length: "255",
        default: "'online'", // String values need quotes
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("bookings", "payment_type");
    await queryRunner.dropColumn("transactions", "payment_type");
  }
}
