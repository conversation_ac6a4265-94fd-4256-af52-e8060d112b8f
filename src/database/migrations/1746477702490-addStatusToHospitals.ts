import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddStatusToHospitals1746477702490 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "hospitals",
      new TableColumn({
        name: "status",
        type: "enum",
        enum: ["pending", "active", "disabled", "rejected"],
        default: "'pending'",
        isNullable: true,
      }),
    );

    await queryRunner.addColumn(
      "doctors",
      new TableColumn({
        name: "status",
        type: "enum",
        enum: ["pending", "active", "disabled", "rejected"],
        default: "'pending'",
        isNullable: true,
      }),
    );
    await queryRunner.dropColumn("hospitals", "current");
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("hospitals", "status");
    await queryRunner.dropColumn("doctors", "status");
  }
}
