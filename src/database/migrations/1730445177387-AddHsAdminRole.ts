import { MigrationInterface, QueryRunner } from "typeorm";

export class AddHsAdminRole1730445177387 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add a new value 'hsAdmin' to the user_type ENUM
    await queryRunner.query(`
          ALTER TABLE users
          MODIFY COLUMN user_type ENUM('superAdmin', 'client', 'user', 'staff', 'hsAdmin') NULL;
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert the ENUM column to exclude 'hsAdmin'
    await queryRunner.query(`
          ALTER TABLE users
          MODIFY COLUMN user_type ENUM('superAdmin', 'client', 'user', 'staff') NULL;
        `);
  }
}
