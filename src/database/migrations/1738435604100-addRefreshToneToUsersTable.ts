import { MigrationInterface, QueryRunner } from "typeorm";

export class AddRefreshToneToUsersTable1738435604100
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      "ALTER TABLE `users` ADD COLUMN `refresh_token` LONGTEXT DEFAULT NULL;",
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query("ALTER TABLE `users` DROP COLUMN `refresh_token`;");
  }
}
