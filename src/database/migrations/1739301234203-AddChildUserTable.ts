import { MigrationInterface, QueryRunner } from "typeorm";

export class AddChildUserTable1739301234207 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
          CREATE TABLE child_users (
            id CHAR(36) NOT NULL PRIMARY KEY,
            fullname VA<PERSON>HAR(255) NOT NULL,
            gender ENUM('male', 'female', 'other') NOT NULL,
            age INT NOT NULL,
            relation VARCHAR(255) NULL,
            appointment_details JSON NULL,
            files TEXT NULL,
            user_id CHAR(36) NOT NULL,
            CONSTRAINT FK_child_users_user FOREIGN KEY (user_id) 
              REFERENCES users(id) ON DELETE CASCADE
          )
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE child_users`);
  }
}
