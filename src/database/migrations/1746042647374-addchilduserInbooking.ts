import {
  MigrationInterface,
  QueryRunner,
  <PERSON>C<PERSON>umn,
  TableForeign<PERSON>ey,
} from "typeorm";

export class AddchilduserInbooking1746042647374 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "bookings",
      new TableColumn({
        name: "child_user_id",
        type: "varchar",
        length: "36", // UUID as varchar in MySQL
        isNullable: true,
      }),
    );

    await queryRunner.addColumn(
      "bookings",
      new TableColumn({
        name: "user_details",
        type: "json",
        isNullable: true,
      }),
    );
    await queryRunner.createForeignKey(
      "bookings",
      new TableForeignKey({
        columnNames: ["child_user_id"],
        referencedTableName: "child_users",
        referencedColumnNames: ["id"],
        onDelete: "CASCADE",
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable("bookings");
    const foreignKeyUser = table?.foreignKeys.find(
      (fk) => fk.columnNames.indexOf("user_id") !== -1,
    );
    if (foreignKeyUser) {
      await queryRunner.dropForeignKey("bookings", foreignKeyUser);
    }
  }
}
