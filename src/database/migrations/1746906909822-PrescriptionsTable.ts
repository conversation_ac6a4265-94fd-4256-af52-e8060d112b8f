import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from "typeorm";

export class PrescriptionsTable1746906909822 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create the "booking_prescriptions" table
    await queryRunner.createTable(
      new Table({
        name: "booking_prescriptions",
        columns: [
          {
            name: "id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isPrimary: true,
            isGenerated: false,
          },
          {
            name: "booking_id",
            type: "varchar",
            length: "36",
            isNullable: false,
          },
          {
            name: "user_id",
            type: "varchar",
            length: "36",
            isNullable: false,
          },
          {
            name: "file",
            type: "varchar",
            length: "255",
            isNullable: false,
          },
          {
            name: "created_at",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
          },
          {
            name: "updated_at",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
            onUpdate: "CURRENT_TIMESTAMP",
          },
        ],
      }),
      true,
    );

    // Add foreign keys to the "booking_prescriptions" table
    await queryRunner.createForeignKeys("booking_prescriptions", [
      new TableForeignKey({
        columnNames: ["booking_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "bookings",
        onDelete: "CASCADE",
      }),
      new TableForeignKey({
        columnNames: ["user_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "users",
        onDelete: "CASCADE",
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the "booking_prescriptions" table
    await queryRunner.dropTable("booking_prescriptions");
  }
}
