import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeign<PERSON>ey,
} from "typeorm";

export class Createbookingstable1738427693116 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: "bookings",
        columns: [
          {
            name: "id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isPrimary: true,
            isGenerated: false, // MySQL doesn't support automatic UUID generation natively
          },
          {
            name: "booking_id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isUnique: true,
          },
          {
            name: "user_id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
          },
          {
            name: "doctor_id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
          },
          {
            name: "hospital_id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isNullable: true,
          },
          {
            name: "appointmentDate",
            type: "datetime",
          },
          {
            name: "timeSlot",
            type: "time",
            isNullable: true,
          },
          {
            name: "status",
            type: "enum",
            enum: [
              "pending",
              "accepted",
              "rejected",
              "completed",
              "cancelled",
              "started",
            ],
            default: "'pending'",
          },
          {
            name: "prescriptionUrl",
            type: "varchar",
            length: "255",
            isNullable: true,
          },
          {
            name: "createdAt",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
          },
          {
            name: "updatedAt",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
            onUpdate: "CURRENT_TIMESTAMP",
          },
        ],
      }),
      true,
    );

    await queryRunner.createForeignKey(
      "bookings",
      new TableForeignKey({
        columnNames: ["user_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "users",
        onDelete: "CASCADE",
      }),
    );

    await queryRunner.createForeignKey(
      "bookings",
      new TableForeignKey({
        columnNames: ["doctor_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "doctors",
        onDelete: "CASCADE",
      }),
    );

    await queryRunner.createForeignKey(
      "bookings",
      new TableForeignKey({
        columnNames: ["hospital_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "hospitals",
        onDelete: "CASCADE",
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable("bookings");
    const foreignKeys = table.foreignKeys;

    await Promise.all(
      foreignKeys.map((foreignKey) =>
        queryRunner.dropForeignKey("bookings", foreignKey),
      ),
    );

    await queryRunner.dropTable("bookings");
  }
}
