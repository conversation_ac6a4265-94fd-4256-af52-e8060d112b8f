import { MigrationInterface, QueryRunner, Table } from "typeorm";

export class CreateAppDetails1747249956823 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: "app_details",
        columns: [
          {
            name: "id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isPrimary: true,
            isGenerated: false,
          },
          {
            name: "app_store_link",
            type: "varchar",
            length: "255",
            isNullable: true,
          },
          {
            name: "play_store_link",
            type: "varchar",
            length: "255",
            isNullable: true,
          },
          {
            name: "platform_fee",
            type: "decimal",
            precision: 8,
            scale: 2,
            isNullable: true,
          },
          {
            name: "version_details_ios",
            type: "json",
            isNullable: true,
          },
          {
            name: "version_details_android",
            type: "json",
            isNullable: true,
          },
          {
            name: "connect_link",
            type: "varchar",
            length: "255",
            isNullable: true,
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable("app_details");
  }
}
