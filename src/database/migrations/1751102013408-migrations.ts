import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class Migrations1751102013408 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "transactions",
      new TableColumn({
        name: "wallet_updated",
        type: "boolean",
        isNullable: true,
        default: false,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("transactions", "wallet_updated");
  }
}
