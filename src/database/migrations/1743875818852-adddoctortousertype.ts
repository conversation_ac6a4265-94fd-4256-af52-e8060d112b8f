import { MigrationInterface, QueryRunner } from "typeorm";

export class Addd<PERSON>tortousertype1743875818852 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE users
            MODIFY COLUMN user_type ENUM('superAdmin', 'client', 'user', 'staff', 'hsAdmin','doctor') NULL;
          `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert the ENUM column to exclude 'hsAdmin'
    await queryRunner.query(`
            ALTER TABLE users
            MODIFY COLUMN user_type ENUM('superAdmin', 'client', 'user', 'staff','hsAdmin') NULL;
          `);
  }
}
