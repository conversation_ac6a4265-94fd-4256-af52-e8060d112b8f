import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddReviews1746556216234 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns("hospitals", [
      new TableColumn({
        name: "total_rating",
        type: "decimal",
        precision: 10,
        scale: 2,
        default: 0,
      }),
      new TableColumn({
        name: "avg_rating",
        type: "decimal",
        precision: 3,
        scale: 2,
        default: 0,
      }),
      new TableColumn({
        name: "rating_count",
        type: "int",
        default: 0,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("hospitals", "total_rating");
    await queryRunner.dropColumn("hospitals", "avg_rating");
    await queryRunner.dropColumn("hospitals", "rating_count");
  }
}
