import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from "typeorm";

export class CeateAvailabilitiesTable1747343405864
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: "availabilities",
        columns: [
          {
            name: "id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isPrimary: true,
            isGenerated: false, // MySQL doesn't support automatic UUID generation natively
          },
          {
            name: "doctor_id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
          },
          {
            name: "week_id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isNullable: true,
          },
          {
            name: "type",
            type: "enum",
            enum: ["week", "date"],
          },
          {
            name: "date",
            type: "date",
            isNullable: true,
          },
          {
            name: "startTime",
            type: "time",
            isNullable: true,
          },
          {
            name: "endTime",
            type: "time",
            isNullable: true,
          },
          {
            name: "not_available",
            type: "boolean",
            default: false,
          },
          {
            name: "createdAt",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
          },
          {
            name: "updatedAt",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
            onUpdate: "CURRENT_TIMESTAMP",
          },
        ],
      }),
      true,
    );

    await queryRunner.createForeignKey(
      "availabilities",
      new TableForeignKey({
        columnNames: ["doctor_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "doctors",
        onDelete: "CASCADE",
      }),
    );

    await queryRunner.createForeignKey(
      "availabilities",
      new TableForeignKey({
        columnNames: ["week_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "weeks",
        onDelete: "CASCADE",
      }),
    );
  }
  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable("availabilities");
    const foreignKeys = table.foreignKeys;

    await Promise.all(
      foreignKeys.map((foreignKey) =>
        queryRunner.dropForeignKey("availabilities", foreignKey),
      ),
    );

    await queryRunner.dropTable("availabilities");
  }
}
