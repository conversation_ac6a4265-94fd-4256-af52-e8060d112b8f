import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddchilduserInbooking1746373933204 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "transactions",
      new TableColumn({
        name: "order_id",
        type: "varchar",
        isNullable: true,
      }),
    );

    await queryRunner.addColumn(
      "transactions",
      new TableColumn({
        name: "type",
        type: "enum",
        enum: ["consultation", "service", "fast_tag"],
        default: "'consultation'",
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("transactions", "order_id");
  }
}
