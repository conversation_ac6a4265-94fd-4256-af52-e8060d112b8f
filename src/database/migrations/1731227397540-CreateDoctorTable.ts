import { MigrationInterface, QueryRunner, Table } from "typeorm";

export class CreateDoctorTable1731227397549 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: "doctors",
        columns: [
          {
            name: "id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isPrimary: true,
            isGenerated: false, // MySQL doesn't support automatic UUID generation natively
          },
          {
            name: "hospital_id",
            type: "varchar",
            length: "36", // Referencing a UUID as a VARCHAR in MySQL
            isNullable: true,
          },
          {
            name: "profile_pic",
            type: "varchar",
            isNullable: true,
          },
          {
            name: "name",
            type: "varchar",
            isNullable: false,
          },
          {
            name: "email",
            type: "varchar",
            isNullable: false,
          },
          {
            name: "department_id",
            type: "json", // MySQL supports JSON natively
            isNullable: true,
          },
          {
            name: "specifications_id",
            type: "varchar",
            length: "36", // UUID stored as a VARCHAR in MySQL
            isNullable: true,
          },
          {
            name: "city",
            type: "varchar",
            isNullable: true,
          },
          {
            name: "registration_details",
            type: "json",
            isNullable: true,
          },
          {
            name: "address",
            type: "json",
            isNullable: true,
          },
          {
            name: "bank_details",
            type: "json",
            isNullable: true,
          },
          {
            name: "documents",
            type: "json",
            isNullable: true,
          },
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable("doctors");
  }
}
