import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class UpdateUserTable1737279224602 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "users",
      new TableColumn({
        name: "roles",
        type: "json", // Using JSON type for storing array of strings
        isNullable: true, // Set to true if you want the roles to be optional
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("users", "roles");
  }
}
