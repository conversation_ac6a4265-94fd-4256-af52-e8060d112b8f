import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddConsultationTimeTodoctors1747339575552
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "doctors",
      new TableColumn({
        name: "consultation_duration",
        type: "int",
        default: 15,
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("doctors", "consultation_duration"); // Added rollback logic
  }
}
