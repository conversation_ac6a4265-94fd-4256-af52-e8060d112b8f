import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddfromWeb1752321459570 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "doctors",
      new TableColumn({
        name: "from_web",
        type: "boolean",
        isNullable: true,
        default: false,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("doctors", "from_web");
  }
}
