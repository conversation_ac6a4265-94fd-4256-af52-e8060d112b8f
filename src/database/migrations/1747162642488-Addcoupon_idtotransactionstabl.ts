import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddcouponIdtotransactionstabl1747162642488
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add the "amount" column
    await queryRunner.addColumn(
      "bookings",
      new TableColumn({
        name: "amount",
        type: "decimal",
        precision: 10,
        scale: 2,
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the "amount" column
    await queryRunner.dropColumn("bookings", "amount");
  }
}
