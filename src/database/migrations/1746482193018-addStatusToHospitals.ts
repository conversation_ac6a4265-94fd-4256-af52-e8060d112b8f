import {
  MigrationInterface,
  QueryRunner,
  TableColumn,
  TableForeign<PERSON>ey,
} from "typeorm";

export class AddStatusToHospitals1746482193018 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "transactions",
      new TableColumn({
        name: "hospital_service_id",
        type: "varchar",
        length: "36", // UUID as varchar in MySQL
        isNullable: true,
      }),
    );
    // await queryRunner.createForeignKey(
    //     "transactions",
    //     new TableForeignKey({
    //         columnNames: ["hospital_service_id"],
    //         referencedColumnNames: ["id"],
    //         referencedTableName: "hospital_services",
    //         onDelete: "CASCADE",
    //     }),
    // );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
