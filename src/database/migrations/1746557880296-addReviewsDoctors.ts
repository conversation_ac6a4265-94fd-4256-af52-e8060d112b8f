import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddReviewsDoctors1746557880296 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns("doctors", [
      new TableColumn({
        name: "total_rating",
        type: "decimal",
        precision: 10,
        scale: 2,
        default: 0,
      }),
      new TableColumn({
        name: "avg_rating",
        type: "decimal",
        precision: 3,
        scale: 2,
        default: 0,
      }),
      new TableColumn({
        name: "rating_count",
        type: "int",
        default: 0,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("doctors", "total_rating");
    await queryRunner.dropColumn("doctors", "avg_rating");
    await queryRunner.dropColumn("doctors", "rating_count");
  }
}
