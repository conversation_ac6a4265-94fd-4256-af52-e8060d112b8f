import { MigrationInterface, QueryRunner } from "typeorm";

export class AddDistrictIdToUsersAndHospitals1738502246906
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add the district_id column
    await queryRunner.query(
      `ALTER TABLE users ADD COLUMN district_id VARCHAR(36) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE hospitals ADD COLUMN district_id VARCHAR(36) NULL`,
    );

    // Add foreign key constraints
    await queryRunner.query(
      `ALTER TABLE users ADD CONSTRAINT FK_users_district FOREIGN KEY (district_id) REFERENCES districts(id) ON DELETE SET NULL`,
    );

    await queryRunner.query(
      `ALTER TABLE hospitals ADD CONSTRAINT FK_hospitals_district FOREIGN KEY (district_id) REFERENCES districts(id) ON DELETE SET NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    await queryRunner.query(
      `ALTER TABLE users DROP FOREIGN KEY FK_users_district`,
    );
    await queryRunner.query(
      `ALTER TABLE hospitals DROP FOREIGN KEY FK_hospitals_district`,
    );

    // Drop the district_id column
    await queryRunner.query(`ALTER TABLE users DROP COLUMN district_id`);
    await queryRunner.query(`ALTER TABLE hospitals DROP COLUMN district_id`);
  }
}
