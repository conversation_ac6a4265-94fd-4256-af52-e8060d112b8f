import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddBookingCountInServicesTable1747253203365
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "hospital_services",
      new TableColumn({
        name: "daily_booking_count",
        type: "int",
        isNullable: true,
        default: 10,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
