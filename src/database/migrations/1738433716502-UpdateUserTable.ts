import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class UpdateUserTable1738433716509 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Change first_name column
    await queryRunner.changeColumn(
      "users",
      "first_name",
      new TableColumn({
        name: "first_name",
        type: "varchar",
        isNullable: true,
      }),
    );

    // Change last_name column
    await queryRunner.changeColumn(
      "users",
      "last_name",
      new TableColumn({
        name: "last_name",
        type: "varchar",
        isNullable: true,
      }),
    );

    // Add otp_secret column
    await queryRunner.addColumn(
      "users",
      new TableColumn({
        name: "otp_secret",
        type: "varchar",
        length: "255",
        isNullable: true,
        comment: "Secret key for OTP generation and verification",
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert first_name column
    await queryRunner.changeColumn(
      "users",
      "first_name",
      new TableColumn({
        name: "first_name",
        type: "varchar",
        isNullable: false,
      }),
    );

    // Revert last_name column
    await queryRunner.changeColumn(
      "users",
      "last_name",
      new TableColumn({
        name: "last_name",
        type: "varchar",
        isNullable: false,
      }),
    );

    // Drop otp_secret column
    await queryRunner.dropColumn("users", "otp_secret");
  }
}
