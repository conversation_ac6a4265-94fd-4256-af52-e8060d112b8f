import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from "typeorm";

export class Firebasetokens1747680739457 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: "firebase_tokens",
        columns: [
          {
            name: "id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isPrimary: true,
            isGenerated: false, // MySQL doesn't support automatic UUID generation natively
          },
          {
            name: "user_id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
          },
          {
            name: "token",
            type: "varchar",
            length: "255",
          },
        ],
      }),
      true,
    );

    await queryRunner.createForeignKey(
      "firebase_tokens",
      new TableForeignKey({
        columnNames: ["user_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "users",
        onDelete: "CASCADE",
      }),
    );
  }
  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable("firebase_tokens");
    const foreignKeys = table.foreignKeys;

    await Promise.all(
      foreignKeys.map((foreignKey) =>
        queryRunner.dropForeignKey("firebase_tokens", foreignKey),
      ),
    );

    await queryRunner.dropTable("firebase_tokens");
  }
}
