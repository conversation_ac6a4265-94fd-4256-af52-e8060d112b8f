import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class Addlinks1747306921174 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "app_details",
      new TableColumn({
        name: "privacy_policy_link",
        type: "varchar",
        isNullable: true,
      }),
    );
    await queryRunner.addColumn(
      "app_details",
      new TableColumn({
        name: "terms_and_conditions_link",
        type: "varchar",
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("app_details", "terms_and_conditions_link");
    await queryRunner.dropColumn("app_details", "privacy_policy_link");
  }
}
