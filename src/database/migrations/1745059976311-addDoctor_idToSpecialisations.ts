import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableColumn,
  TableForeignKey,
} from "typeorm";

export class AddDoctorIdToSpecialisations1745059976311
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: "doctor_specialisation",
        columns: [
          {
            name: "id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isPrimary: true,
            isGenerated: false,
          },
          {
            name: "doctor_id",
            type: "varchar",
            length: "36",
            isNullable: false,
          },
          {
            name: "specialisation_id",
            type: "varchar",
            length: "36",
            isNullable: false,
          },
        ],
      }),
    );

    // Add foreign key constraint to `user_id`
    await queryRunner.createForeignKey(
      "doctor_specialisation",
      new TableForeignKey({
        columnNames: ["doctor_id"],
        referencedTableName: "doctors",
        referencedColumnNames: ["id"],
        onDelete: "CASCADE",
      }),
    );

    // Add foreign key constraint to `specialisation_id`
    await queryRunner.createForeignKey(
      "doctor_specialisation",
      new TableForeignKey({
        columnNames: ["specialisation_id"],
        referencedTableName: "specialisations",
        referencedColumnNames: ["id"],
        onDelete: "CASCADE",
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    const table = await queryRunner.getTable("doctor-specialisation");
    const foreignKeyUser = table?.foreignKeys.find(
      (fk) => fk.columnNames.indexOf("user_id") !== -1,
    );
    if (foreignKeyUser) {
      await queryRunner.dropForeignKey("doctor-specialisation", foreignKeyUser);
    }

    const foreignKeySpecialisation = table?.foreignKeys.find(
      (fk) => fk.columnNames.indexOf("specialisation_id") !== -1,
    );
    if (foreignKeySpecialisation) {
      await queryRunner.dropForeignKey(
        "doctor-specialisation",
        foreignKeySpecialisation,
      );
    }

    // Drop `doctor-specialisation` table
    await queryRunner.dropTable("doctor-specialisation");
  }
}
