import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class Addaboutinhospita1754161029449 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
         "hospitals",
         new TableColumn({
           name: "about",
           type: "varchar",
           isNullable: true,
         }),
       );
     }
   
     public async down(queryRunner: QueryRunner): Promise<void> {
       await queryRunner.dropColumn("hospitals", "about");
     }
    }