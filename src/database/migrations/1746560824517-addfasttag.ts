import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class Addfasttag1746560824517 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "bookings",
      new TableColumn({
        name: "is_fast_tag",
        type: "boolean",
        isNullable: true,
        default: false,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("bookings", "is_fast_tag");
  }
}
