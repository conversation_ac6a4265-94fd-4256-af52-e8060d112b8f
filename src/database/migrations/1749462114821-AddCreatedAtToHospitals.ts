import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddCreatedAtToHospitals1749462114821
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "hospitals",
      new TableColumn({
        name: "created_at",
        type: "timestamp",
        default: "CURRENT_TIMESTAMP",
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE hospitals DROP COLUMN created_at`);
  }
}
