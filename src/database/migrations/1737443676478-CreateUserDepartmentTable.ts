import { MigrationInterface, QueryRunner, Table } from "typeorm";

export class CreateUserDepartmentTable1737443676478
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: "user_departments",
        columns: [
          {
            name: "id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isPrimary: true,
            isGenerated: false, // MySQL doesn't support automatic UUID generation natively
          },
          {
            name: "department_id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isNullable: false,
          },
          {
            name: "user_id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isNullable: false,
          },
          {
            name: "doctor_id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isNullable: true,
          },
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable("user_departments");
  }
}
