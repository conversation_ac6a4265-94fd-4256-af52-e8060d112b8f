import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from "typeorm";

export class CreateDepartmentsTable1737275989618 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create `departments` table
    await queryRunner.createTable(
      new Table({
        name: "departments",
        columns: [
          {
            name: "id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isPrimary: true,
            isGenerated: false, // MySQL doesn't support automatic UUID generation natively
          },
          {
            name: "name",
            type: "varchar",
            length: "255",
            isNullable: false,
          },
          {
            name: "description",
            type: "text",
            isNullable: true,
          },
          {
            name: "hospital_id",
            type: "varchar",
            length: "36",
            isNullable: false,
          },
        ],
      }),
    );

    // Add foreign key constraint to `hospital_id`
    await queryRunner.createForeignKey(
      "departments",
      new TableForeignKey({
        columnNames: ["hospital_id"],
        referencedTableName: "hospitals",
        referencedColumnNames: ["id"],
        onDelete: "CASCADE",
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraint
    const table = await queryRunner.getTable("departments");
    const foreignKey = table?.foreignKeys.find(
      (fk) => fk.columnNames.indexOf("hospital_id") !== -1,
    );
    if (foreignKey) {
      await queryRunner.dropForeignKey("departments", foreignKey);
    }

    // Drop `departments` table
    await queryRunner.dropTable("departments");
  }
}
