import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class Addforgotpasswordsecret1752395669336
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "users",
      new TableColumn({
        name: "otp_secret_forgot_password",
        type: "varchar",
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("users", "otp_secret_forgot_password");
  }
}
