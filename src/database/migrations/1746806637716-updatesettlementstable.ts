import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class Updatesettlementstable1746806637716 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "settlements",
      new TableColumn({
        name: "transaction_id",
        type: "varchar",
        length: "36",
        isNullable: true,
        isUnique: true,
      }),
    );

    await queryRunner.addColumn(
      "settlements",
      new TableColumn({
        name: "payment_method",
        type: "enum",
        enum: ["bank_transfer", "cash", "cheque", "mobile_money"],
        default: "'bank_transfer'",
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("settlements", "payment_method");
    await queryRunner.dropColumn("settlements", "transaction_id");
  }
}
