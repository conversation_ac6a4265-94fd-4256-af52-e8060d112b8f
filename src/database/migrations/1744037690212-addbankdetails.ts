import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class Addbankdetails1744037690212 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "hospitals",
      new TableColumn({
        name: "bank_details",
        type: "json",
        isNullable: true,
      }),
    );
    await queryRunner.addColumn(
      "hospitals",
      new TableColumn({
        name: "company_details",
        type: "json",
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("hospitals", "bank_details");
    await queryRunner.dropColumn("hospitals", "company_details");
  }
}
