import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class Addaddresstodepartments1746019406759
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "departments",
      new TableColumn({
        name: "status",
        type: "enum",
        enum: ["active", "inactive"],
        default: "'active'",
      }),
    );
    await queryRunner.addColumn(
      "departments",
      new TableColumn({
        name: "category",
        type: "varchar",
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("departments", "status");
    await queryRunner.dropColumn("departments", "category");
  }
}
