import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class Addminratetohospitalstable1745063862197
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "hospitals",
      new TableColumn({
        name: "min_rate",
        type: "decimal",
        precision: 10,
        scale: 2,
        isNullable: true,
      }),
    );
    await queryRunner.addColumn(
      "hospitals",
      new TableColumn({
        name: "max_rate",
        type: "decimal",
        precision: 10,
        scale: 2,
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("hospitals", "min_rate");
    await queryRunner.dropColumn("hospitals", "max_rate");
  }
}
