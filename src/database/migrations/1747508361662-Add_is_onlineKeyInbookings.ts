import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddIsOnlineKeyInbookings1747508361662
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "bookings",
      new TableColumn({
        name: "is_online",
        type: "boolean",
        default: true,
        isNullable: true,
      }),
    );
    await queryRunner.addColumn(
      "bookings",
      new TableColumn({
        name: "token_number",
        type: "int",
        isNullable: true,
      }),
    );
    await queryRunner.addColumn(
      "bookings",
      new TableColumn({
        name: "completed_at",
        type: "timestamp",
        default: null,
        isNullable: true,
      }),
    );
    await queryRunner.addColumn(
      "transactions",
      new TableColumn({
        name: "is_online",
        type: "boolean",
        default: true,
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("bookings", "is_online");
    await queryRunner.dropColumn("bookings", "token_number");
    await queryRunner.dropColumn("bookings", "completed_at");
    await queryRunner.dropColumn("transactions", "is_online");
  }
}
