import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class Addaddresstousers1745956755168 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "users",
      new TableColumn({
        name: "address",
        type: "json",
        isNullable: true,
      }),
    );
    await queryRunner.addColumn(
      "users",
      new TableColumn({
        name: "job_title",
        type: "varchar",
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("users", "address");
    await queryRunner.dropColumn("users", "job_title");
  }
}
