import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeign<PERSON>ey,
} from "typeorm";

export class Transactionstale1746034807076 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: "transactions",
        columns: [
          {
            name: "id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isPrimary: true,
            isGenerated: false, // MySQL doesn't support automatic UUID generation natively
          },
          {
            name: "user_id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
          },
          {
            name: "doctor_id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isNullable: true,
          },

          {
            name: "hospital_id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isNullable: true,
          },
          {
            name: "booking_id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isNullable: true,
          },
          {
            name: "is_settled",
            type: "boolean",
            isNullable: true,
            default: false,
          },
          {
            name: "paid_date",
            type: "datetime",
          },
          {
            name: "amount",
            type: "decimal",
            precision: 10,
            scale: 2,
            isNullable: true,
          },
          {
            name: "status",
            type: "enum",
            enum: ["started", "completed", "cancelled"],
            default: "'started'",
          },
          {
            name: "note",
            type: "varchar",
            length: "255",
            isNullable: true,
          },
          {
            name: "created_at",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
          },
          {
            name: "updated_at",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
            onUpdate: "CURRENT_TIMESTAMP",
          },
        ],
      }),
      true,
    );

    await queryRunner.createForeignKey(
      "transactions",
      new TableForeignKey({
        columnNames: ["user_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "users",
        onDelete: "CASCADE",
      }),
    );

    await queryRunner.createForeignKey(
      "transactions",
      new TableForeignKey({
        columnNames: ["doctor_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "doctors",
        onDelete: "CASCADE",
      }),
    );

    await queryRunner.createForeignKey(
      "transactions",
      new TableForeignKey({
        columnNames: ["hospital_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "hospitals",
        onDelete: "CASCADE",
      }),
    );

    await queryRunner.createForeignKey(
      "transactions",
      new TableForeignKey({
        columnNames: ["booking_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "bookings",
        onDelete: "CASCADE",
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable("transactions");
    const foreignKeys = table.foreignKeys;

    await Promise.all(
      foreignKeys.map((foreignKey) =>
        queryRunner.dropForeignKey("transactions", foreignKey),
      ),
    );

    await queryRunner.dropTable("transactions");
  }
}
