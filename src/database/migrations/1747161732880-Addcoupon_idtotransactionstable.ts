import {
  MigrationInterface,
  QueryRunner,
  <PERSON><PERSON><PERSON>umn,
  TableForeign<PERSON>ey,
} from "typeorm";

export class AddcouponIdtotransactionstable1747161732880
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "transactions",
      new TableColumn({
        name: "coupon_id",
        type: "varchar",
        scale: 36,
        isNullable: true,
      }),
    );

    // Add foreign key for "coupon_id" referencing "users" table
    // await queryRunner.createForeignKey(
    //   "transactions",
    //   new TableForeignKey({
    //     columnNames: ["coupon_id"],
    //     referencedColumnNames: ["id"],
    //     referencedTableName: "coupons",
    //     onDelete: "CASCADE"
    //   }),
    // );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the foreign key for "coupon_id"
    // const table = await queryRunner.getTable("transactions");
    // const foreignKey = table.foreignKeys.find(
    //   (fk) => fk.columnNames.indexOf("coupon_id") !== -1,
    // );
    // if (foreignKey) {
    //   await queryRunner.dropForeignKey("transactions", foreignKey);
    // }

    // Drop the "coupon_id" column
    await queryRunner.dropColumn("transactions", "coupon_id");
  }
}
