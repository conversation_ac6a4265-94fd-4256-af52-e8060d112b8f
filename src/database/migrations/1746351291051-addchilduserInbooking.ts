import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddchilduserInbooking1746351291051 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "hospitals",
      new TableColumn({
        name: "is_fast_tag_enabled",
        type: "boolean",
        isNullable: true,
      }),
    );

    await queryRunner.addColumn(
      "doctors",
      new TableColumn({
        name: "work_start_date",
        type: "boolean",
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("hospitals", "is_fast_tag_enabled");
  }
}
