import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddExcerciseType1746474885265 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // await queryRunner.addColumn(
    //   "hospitals",
    //   new TableColumn({
    //     name: "bank_details",
    //     type: "json",
    //     isNullable: true,
    //   }),
    // );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // await queryRunner.dropColumn("hospitals", "bank_details");
  }
}
