import {
  MigrationInterface,
  QueryRunner,
  <PERSON><PERSON><PERSON>umn,
  TableForeign<PERSON>ey,
} from "typeorm";

export class AddmetaandbookedByToBookingsTable1747160957699
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add the "meta_data" column
    await queryRunner.addColumn(
      "bookings",
      new TableColumn({
        name: "meta_data",
        type: "json",
        isNullable: true,
      }),
    );

    // Add the "created_by" column
    await queryRunner.addColumn(
      "bookings",
      new TableColumn({
        name: "created_by",
        type: "varchar",
        scale: 36,
        isNullable: true,
      }),
    );

    // Add foreign key for "created_by" referencing "users" table
    await queryRunner.createForeignKey(
      "bookings",
      new TableForeignKey({
        columnNames: ["created_by"],
        referencedColumnNames: ["id"],
        referencedTableName: "users",
        onDelete: "SET NULL", // Set to NULL if the referenced user is deleted
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the foreign key for "created_by"
    const table = await queryRunner.getTable("bookings");
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf("created_by") !== -1,
    );
    if (foreignKey) {
      await queryRunner.dropForeignKey("bookings", foreignKey);
    }

    // Drop the "created_by" column
    await queryRunner.dropColumn("bookings", "created_by");

    // Drop the "meta_data" column
    await queryRunner.dropColumn("bookings", "meta_data");
  }
}
