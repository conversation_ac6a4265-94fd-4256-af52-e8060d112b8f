import { MigrationInterface, QueryRunner } from "typeorm";

export class RemoveSpecIdAndDeptIdFromDoctors1738178587019
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`doctors\` DROP COLUMN \`specifications_id\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`doctors\` DROP COLUMN \`department_id\``,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`doctors\` ADD COLUMN \`specifications_id\` TEXT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`doctors\` ADD COLUMN \`department_id\` TEXT NULL`,
    );
  }
}
