import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class UpdateDoctorTableForPricing1738064717256
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "doctors",
      new TableColumn({
        name: "phone_number",
        type: "varchar",
        isNullable: false,
      }),
    );

    await queryRunner.addColumn(
      "doctors",
      new TableColumn({
        name: "pricing",
        type: "decimal",
        precision: 10,
        scale: 2,
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("doctors", "phone_number");
    await queryRunner.dropColumn("doctors", "pricing");
  }
}
