import {
  MigrationInterface,
  QueryRunner,
  TableColumn,
  TableForeignKey,
} from "typeorm";

export class AddGender1743882134962 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop the existing hospital_id column from the doctors table
    await queryRunner.dropColumn("doctors", "hospital_id");

    // Add a new hospital_id column to the doctors table
    await queryRunner.addColumn(
      "doctors",
      new TableColumn({
        name: "hospital_id",
        type: "varchar",
        scale: 36,
        isNullable: true,
      }),
    );

    // Create a foreign key to connect the hospital_id column to the hospitals table
    await queryRunner.createForeignKey(
      "doctors",
      new TableForeignKey({
        columnNames: ["hospital_id"],
        referencedTableName: "hospitals",
        referencedColumnNames: ["id"],
        onDelete: "CASCADE",
        onUpdate: "CASCADE",
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Retrieve the table to get the foreign key
    const table = await queryRunner.getTable("doctors");
    if (table) {
      const foreignKey = table.foreignKeys.find((fk) =>
        fk.columnNames.includes("hospital_id"),
      );
      if (foreignKey) {
        // Drop the foreign key constraint
        await queryRunner.dropForeignKey("doctors", foreignKey);
      }
    }

    // Drop the hospital_id column
    await queryRunner.dropColumn("doctors", "hospital_id");

    // Add the hospital_id column back to the doctors table
    await queryRunner.addColumn(
      "doctors",
      new TableColumn({
        name: "hospital_id",
        type: "varchar",
        isNullable: true,
      }),
    );
  }
}
