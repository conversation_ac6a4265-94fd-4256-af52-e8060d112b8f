import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeign<PERSON>ey,
} from "typeorm";

export class CreateUserRolesTable1725028181742 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create the role_user table
    await queryRunner.createTable(
      new Table({
        name: "role_user",
        columns: [
          {
            name: "role_id",
            type: "varchar",
            length: "36", // UUID stored as varchar in MySQL
          },
          {
            name: "user_id",
            type: "varchar",
            length: "36", // UUID stored as varchar in MySQL
          },
        ],
        // Setting both 'role_id' and 'user_id' as composite primary keys
        uniques: [{ columnNames: ["role_id", "user_id"] }],
      }),
    );

    // Create foreign key for role_id
    await queryRunner.createForeignKey(
      "role_user",
      new TableForeignKey({
        columnNames: ["role_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "roles",
        onDelete: "CASCADE",
      }),
    );

    // Create foreign key for user_id
    await queryRunner.createForeignKey(
      "role_user",
      new TableForeign<PERSON>ey({
        columnNames: ["user_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "users",
        onDelete: "CASCADE",
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the table (this will also drop the foreign keys)
    await queryRunner.dropTable("role_user");
  }
}
