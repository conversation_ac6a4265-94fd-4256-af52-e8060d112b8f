import {
  MigrationInterface,
  QueryRunner,
  TableColumn,
  TableForeignKey,
} from "typeorm";

export class AddDistrictIdToHospitalServices1751737362370
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "hospital_services",
      new TableColumn({
        name: "district_id",
        type: "varchar",
        length: "36", // UUID as varchar in MySQL
        isNullable: true,
      }),
    );

    await queryRunner.createForeignKey(
      "hospital_services",
      new TableForeignKey({
        columnNames: ["district_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "districts",
        onDelete: "SET NULL",
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable("hospital_services");
    const foreignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf("district_id") !== -1,
    );
    await queryRunner.dropForeignKey("hospital_services", foreignKey);
    await queryRunner.dropColumn("hospital_services", "district_id");
  }
}
