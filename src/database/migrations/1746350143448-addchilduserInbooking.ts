import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddchilduserInbooking1746350143448 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "users",
      new TableColumn({
        name: "profile_picture",
        type: "varchar",
        isNullable: true,
      }),
    );
    await queryRunner.addColumn(
      "child_users",
      new TableColumn({
        name: "profile_picture",
        type: "varchar",
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("users", "profile_picture");
    await queryRunner.dropColumn("child_users", "profile_picture");
  }
}
