import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from "typeorm";

export class Couponstable1746729271755 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: "coupons",
        columns: [
          {
            name: "id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isPrimary: true,
            isGenerated: false,
          },
          {
            name: "coupon_code",
            type: "varchar",
            length: "255",
          },
          {
            name: "discount_type",
            type: "enum",
            enum: ["flat", "percentage"],
          },
          {
            name: "valid_untill",
            type: "datetime",
            isNullable: true,
          },
          {
            name: "minimum_cart_amount",
            type: "decimal",
            precision: 8,
            scale: 2,
            isNullable: true,
          },
          {
            name: "minimum_discount_allowed",
            type: "decimal",
            precision: 8,
            scale: 2,
            isNullable: true,
          },

          {
            name: "usage_limit",
            type: "int",
            isNullable: true,
          },
          {
            name: "usage_limit_per_user",
            type: "int",
            default: 1,
            isNullable: true,
          },
          {
            name: "usage_count",
            type: "int",
            default: 0,
          },
          {
            name: "hospital_id",
            type: "varchar",
            scale: 36,
            isNullable: true,
          },
          {
            name: "deleted_at",
            type: "timestamp",
            isNullable: true,
          },
          {
            name: "created_at",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
          },
          {
            name: "updated_at",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
            onUpdate: "CURRENT_TIMESTAMP",
          },
        ],
      }),
      true,
    );

    await queryRunner.createForeignKey(
      "coupons",
      new TableForeignKey({
        columnNames: ["hospital_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "hospitals",
        onDelete: "CASCADE",
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable("coupons");
  }
}
