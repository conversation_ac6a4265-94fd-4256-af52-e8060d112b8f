import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddAutoBookingEnabledToDoctors1747891234567
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "doctors",
      new TableColumn({
        name: "auto_booking_enabled",
        type: "boolean",
        default: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("doctors", "auto_booking_enabled");
  }
}
