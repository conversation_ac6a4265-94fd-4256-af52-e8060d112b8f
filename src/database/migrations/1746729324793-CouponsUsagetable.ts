import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from "typeorm";

export class CouponsUsagetable1746729324793 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: "coupon_usage",
        columns: [
          {
            name: "id",
            type: "bigint",
            isPrimary: true,
            isGenerated: true,
            generationStrategy: "increment",
            unsigned: true,
          },
          {
            name: "coupon_id",
            type: "varchar",
            scale: 36,
            isNullable: true,
          },
          {
            name: "user_id",
            type: "varchar",
            scale: 36,
            isNullable: true,
          },
          {
            name: "hospital_id",
            type: "varchar",
            scale: 36,
            isNullable: true,
          },
        ],
      }),
      true,
    );

    await queryRunner.createForeignKeys("coupon_usage", [
      new TableForeignKey({
        columnNames: ["coupon_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "coupons",
        onDelete: "CASCADE",
      }),
      new TableForeignKey({
        columnNames: ["user_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "users",
        onDelete: "CASCADE",
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable("coupon_usage");
  }
}
