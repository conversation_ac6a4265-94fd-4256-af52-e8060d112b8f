import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class Updatesettlementstable1746906656863 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "bookings",
      new TableColumn({
        name: "reason",
        type: "varchar",
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("bookings", "reason");
  }
}
