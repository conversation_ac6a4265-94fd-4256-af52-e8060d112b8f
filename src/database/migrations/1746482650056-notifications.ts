import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from "typeorm";

export class Notifications1746482650056 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: "notifications",
        columns: [
          {
            name: "id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
            isPrimary: true,
            isGenerated: false, // MySQL doesn't support automatic UUID generation natively
          },
          {
            name: "user_id",
            type: "varchar",
            length: "36", // UUID as varchar in MySQL
          },

          {
            name: "title",
            type: "varchar",
          },
          {
            name: "body",
            type: "varchar",
            isNullable: true,
          },
          {
            name: "is_seen",
            type: "boolean",
            isNullable: true,
            default: false,
          },
          {
            name: "created_at",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
          },
          {
            name: "updated_at",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
            onUpdate: "CURRENT_TIMESTAMP",
          },
        ],
      }),
      true,
    );

    await queryRunner.createForeignKey(
      "notifications",
      new TableForeignKey({
        columnNames: ["user_id"],
        referencedColumnNames: ["id"],
        referencedTableName: "users",
        onDelete: "CASCADE",
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable("notifications");
    const foreignKeys = table.foreignKeys;

    await Promise.all(
      foreignKeys.map((foreignKey) =>
        queryRunner.dropForeignKey("notifications", foreignKey),
      ),
    );

    await queryRunner.dropTable("notifications");
  }
}
