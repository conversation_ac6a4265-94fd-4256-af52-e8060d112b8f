import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddGender1743878569888 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "users",
      new TableColumn({
        name: "gender",
        type: "enum",
        enum: ["male", "female", "other"],
        isNullable: true,
      }),
    );

    await queryRunner.addColumn(
      "users",
      new TableColumn({
        name: "age",
        type: "int",
        isNullable: true,
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("users", "gender");
    await queryRunner.dropColumn("users", "age");

    // Drop enum type if you're using Postgres
    await queryRunner.query(`DROP TYPE IF EXISTS "users_gender_enum"`);
  }
}
