import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddUserIdToDoctorsTable1731244612529
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      "doctors",
      new TableColumn({
        name: "user_id",
        type: "varchar",
        length: "36", // UUID stored as VARCHAR(36) in MySQL
        isNullable: true, // Makes it optional
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn("doctors", "user_id");
  }
}
