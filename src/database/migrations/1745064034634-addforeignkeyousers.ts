import { MigrationInterface, QueryRunner, TableFore<PERSON><PERSON><PERSON> } from "typeorm";

export class Addforeignkeyousers1745064034634 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createForeignKey(
      "doctors",
      new TableForeignKey({
        columnNames: ["user_id"],
        referencedTableName: "users",
        referencedColumnNames: ["id"],
        onDelete: "CASCADE",
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable("doctors");
    const foreignKeyUser = table?.foreignKeys.find(
      (fk) => fk.columnNames.indexOf("user_id") !== -1,
    );
    if (foreignKeyUser) {
      await queryRunner.dropForeignKey("doctors", foreignKeyUser);
    }
  }
}
