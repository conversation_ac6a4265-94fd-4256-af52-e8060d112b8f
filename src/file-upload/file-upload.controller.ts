import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from "@nestjs/common";
import { FileUploadService } from "./file-upload.service";
import { AccessTokenGuard } from "src/common/guards/accessToken.guard";
import { FileInterceptor } from "@nestjs/platform-express";
import { DeleteFileDto } from "./dto/delete-file.dto";

@Controller("file-upload")
export class FileUploadController {
  constructor(private readonly fileUploadService: FileUploadService) {}
  @Post("")
  // @UseGuards(AccessTokenGuard)
  @UseInterceptors(
    FileInterceptor("file", {
      limits: { fileSize: 6 * 1024 * 1024 }, // Set file size limit to 1MB
    }),
  )
  async uploadPublicFile(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException("No file provided or file is too large");
    }

    return this.fileUploadService.uploadPublicFile(
      file.buffer,
      file.originalname,
    );
  }

  @Post("delete")
  // @UseGuards(AccessTokenGuard)
  async deletePublicFile(@Body() deleteFileDto: DeleteFileDto) {
    return this.fileUploadService.deletePublicFile(deleteFileDto);
  }

  @Post("signed-url")
  // @UseGuards(AccessTokenGuard)
  async getSignedUrl(@Body() deleteFileDto: DeleteFileDto) {
    return this.fileUploadService.getSignedUrl(deleteFileDto.fileId);
  }
}
