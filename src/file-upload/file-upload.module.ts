import { Module } from "@nestjs/common";
import { FileUploadService } from "./file-upload.service";
import { FileUploadController } from "./file-upload.controller";
import { CommonModule } from "src/common/common.module";

@Module({
  imports: [CommonModule],
  controllers: [FileUploadController],
  providers: [FileUploadService],
  exports: [FileUploadService], // Export the service to use in other modules
})
export class FileUploadModule {}
