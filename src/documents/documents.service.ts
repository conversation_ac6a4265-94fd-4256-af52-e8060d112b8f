import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { IsNull, Not, Repository } from "typeorm";
import { Document } from "./entities/document.entity";
import { CreateDocumentDto } from "./dto/create-document.dto";
import { UpdateDocumentDto } from "./dto/update-document.dto";
import { paginate, PaginateQuery } from "nestjs-paginate";
import { CreateMultipleDocumentDto } from "./dto/create-multiple-files.dto";
import { FileUploadService } from "src/file-upload/file-upload.service";

@Injectable()
export class DocumentsService {
  constructor(
    @InjectRepository(Document)
    private readonly documentsRepository: Repository<Document>,
    private readonly uploadService: FileUploadService,
  ) {}

  async create(createDocumentDto: CreateDocumentDto): Promise<Document> {
    const document = this.documentsRepository.create(createDocumentDto);
    return this.documentsRepository.save(document);
  }

  async findAll(query: PaginateQuery, filters: any): Promise<any> {
    let where: any = {
      id: Not(IsNull()),
    };

    // Filter by name
    if (filters.hospital_id) {
      where.hospital_id = filters.hospital_id;
    }
    if (filters.doctor_id) {
      where.doctor_id = filters.doctor_id;
    }
    const data = await paginate(query, this.documentsRepository, {
      sortableColumns: ["id"],
      searchableColumns: ["name"],
      where,
    });
    const updatedData = await Promise.all(
      data.data.map(async (prescription) => {
        const signedUrl = await this.uploadService.getSignedUrl(
          prescription.file,
        ); // Call the upload service
        return {
          ...prescription,
          file: signedUrl, // Replace the file key with the signed URL
        };
      }),
    );

    return {
      ...data,
      data: updatedData, // Replace the original data with updated data
    };
  }

  async findOne(id: string): Promise<Document> {
    const document = await this.documentsRepository.findOne({ where: { id } });
    if (!document) {
      throw new NotFoundException(`Document with ID ${id} not found`);
    }
    return document;
  }

  async update(
    id: string,
    updateDocumentDto: UpdateDocumentDto,
  ): Promise<Document> {
    const document = await this.findOne(id);
    Object.assign(document, updateDocumentDto);
    return this.documentsRepository.save(document);
  }

  async remove(id: string): Promise<any> {
    const result = await this.documentsRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Document with ID ${id} not found`);
    }
    return {
      status: true,
      message: "success",
    };
  }

  async createMultipleFiles(dto: CreateMultipleDocumentDto): Promise<any> {
    const { hospital_id, doctor_id, documents } = dto;

    // Map through the files array and create document entities
    const savedDocuments = documents.map((file) => {
      return this.documentsRepository.create({
        hospital_id,
        doctor_id,
        name: file.name,
        file: file.file,
      });
    });

    // Save all documents in the database
    await this.documentsRepository.save(savedDocuments);
    return {
      status: true,
      message: "success",
    };
  }
}
