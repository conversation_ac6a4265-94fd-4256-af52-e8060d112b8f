import {
  Is<PERSON>ptional,
  IsS<PERSON>,
  ValidateNested,
  IsArray,
  IsNotEmpty,
} from "class-validator";
import { Type } from "class-transformer";

export class FileDto {
  @IsOptional()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  file: string;
}

export class CreateMultipleDocumentDto {
  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true }) // Validate each object in the array
  @Type(() => FileDto) // Transform each item in the array to an instance of FileDto
  documents?: FileDto[];

  @IsOptional()
  @IsString()
  doctor_id?: string;

  @IsOptional()
  @IsString()
  hospital_id?: string;

  // @IsNotEmpty()
  // @IsString()
  // files?: string[];
}
