import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import { Doctor } from "src/doctors/entities/doctor.entity";
import { Hospital } from "src/hospitals/entities/hospital.entity";

@Entity("documents")
export class Document {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  name: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  file: string;

  @Column({ type: "varchar", length: 36, nullable: true })
  doctor_id: string | null;

  @Column({ type: "varchar", length: 36, nullable: true })
  hospital_id: string | null;

  @ManyToOne(() => Doctor, { nullable: true, onDelete: "SET NULL" })
  @JoinColumn({ name: "doctor_id" })
  doctor: Doctor;

  @ManyToOne(() => Hospital, { nullable: true, onDelete: "SET NULL" })
  @JoinColumn({ name: "hospital_id" })
  hospital: Hospital;
}
