import {
  IsString,
  <PERSON><PERSON>umber,
  IsOptional,
  IsEnum,
  IsNotEmpty,
  IsDecimal,
} from "class-validator";
import { PaymentMethod, SettlementStatus } from "../entities/settlement.entity";

export class CreateSettlementDto {
  @IsString()
  @IsOptional()
  doctor_id?: string;

  @IsString()
  @IsOptional()
  hospital_id?: string;

  @IsNotEmpty()
  @IsDecimal()
  amount?: number;

  @IsEnum(PaymentMethod)
  @IsOptional()
  payment_method?: PaymentMethod;

  @IsString()
  @IsOptional()
  note?: string;
}
