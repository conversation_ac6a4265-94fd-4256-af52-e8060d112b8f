import { Doctor } from "src/doctors/entities/doctor.entity";
import { Hospital } from "src/hospitals/entities/hospital.entity";
import { User } from "src/users/entities/user.entity";
import {
  Entity,
  PrimaryColumn,
  Column,
  ManyToOne,
  JoinColumn,
  PrimaryGeneratedColumn,
} from "typeorm";

// Define the PaymentMethod enum
export enum PaymentMethod {
  BANK_TRANSFER = "bank_transfer",
  CASH = "cash",
  CHEQUE = "cheque",
  MOBILE_MONEY = "mobile_money",
}

// Define the Status enum
export enum SettlementStatus {
  REQUESTED = "requested",
  APPROVED = "approved",
  REJECTED = "rejected",
}

@Entity("settlements")
export class Settlement {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 36 })
  user_id: string;

  @Column({ type: "varchar", length: 36, nullable: true })
  doctor_id: string;

  @Column({ type: "varchar", length: 36, nullable: true })
  hospital_id: string;

  @Column({ type: "datetime" })
  request_date: Date;

  @Column({ type: "decimal", precision: 10, scale: 2, nullable: true })
  amount: number;

  @Column({
    type: "enum",
    enum: SettlementStatus, // Use the defined enum here
    default: SettlementStatus.REQUESTED,
  })
  status: SettlementStatus;

  @Column({ type: "varchar", length: 255, nullable: true })
  note: string;

  @Column({ type: "datetime", nullable: true })
  approve_date: Date;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  created_at: Date;

  @Column({
    type: "timestamp",
    default: () => "CURRENT_TIMESTAMP",
    onUpdate: "CURRENT_TIMESTAMP",
  })
  updated_at: Date;

  @Column({ type: "varchar", length: 36, nullable: true, unique: true })
  transaction_id: string;

  @Column({
    type: "enum",
    enum: PaymentMethod, // Use the defined enum here
    default: PaymentMethod.BANK_TRANSFER,
    nullable: true,
  })
  payment_method: PaymentMethod;

  @ManyToOne(() => Hospital)
  @JoinColumn({ name: "hospital_id" })
  hospital: Hospital;

  @ManyToOne(() => User)
  @JoinColumn({ name: "user_id" })
  user: User;

  @ManyToOne(() => Doctor)
  @JoinColumn({ name: "doctor_id" })
  doctor: Doctor;
}
