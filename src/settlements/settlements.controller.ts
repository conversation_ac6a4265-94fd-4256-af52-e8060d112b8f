import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Body,
  UseGuards,
  Req,
  Res,
  Query,
  BadRequestException,
} from "@nestjs/common";
import { SettlementsService } from "./settlements.service";
import { CreateSettlementDto } from "./dto/create-settlement.dto";
import { UpdateSettlementDto } from "./dto/update-settlement.dto";
import { ChangeStatusDto } from "./dto/change-status.dto";
import { AccessTokenGuard } from "src/common/guards/accessToken.guard";
import { Paginate, PaginateQuery } from "nestjs-paginate";
import { Response } from "express";

@Controller("settlements")
export class SettlementsController {
  constructor(private readonly settlementsService: SettlementsService) {}

  @UseGuards(AccessTokenGuard)
  @Post()
  create(@Body() createSettlementDto: CreateSettlementDto, @Req() req: any) {
    return this.settlementsService.create(createSettlementDto, req["uid"]);
  }

  @UseGuards(AccessTokenGuard)
  @Get()
  findAll(@Paginate() query: PaginateQuery, @Query() filters: any) {
    if (!filters.hospital_id) {
      throw new BadRequestException("hospital_id is required");
    }
    return this.settlementsService.findAll(query, filters);
  }

  @UseGuards(AccessTokenGuard)
  @Get("admin")
  findAllAdmin(@Paginate() query: PaginateQuery, @Query() filters: any) {
    return this.settlementsService.findAllAdmin(query, filters);
  }

  @Get("export")
  async exportHospitalSettlements(
    @Query() filters: any,
    @Res() response: Response,
  ) {
    if (!filters.hospital_id) {
      throw new BadRequestException("hospital_id is required");
    }
    const { buffer, filename } =
      await this.settlementsService.exportHospitalSettlements(filters);
    response.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    );
    response.setHeader(
      "Content-Disposition",
      `attachment; filename=${filename}`,
    );
    response.send(buffer);
  }

  @Get("admin/export")
  async exportAdminSettlements(
    @Query() filters: any,
    @Res() response: Response,
  ) {
    const { buffer, filename } =
      await this.settlementsService.exportAdminSettlements(filters);
    response.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    );
    response.setHeader(
      "Content-Disposition",
      `attachment; filename=${filename}`,
    );
    response.send(buffer);
  }

  @UseGuards(AccessTokenGuard)
  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.settlementsService.findOne(id);
  }

  @UseGuards(AccessTokenGuard)
  @Patch(":id")
  update(
    @Param("id") id: string,
    @Body() updateSettlementDto: UpdateSettlementDto,
  ) {
    return this.settlementsService.update(id, updateSettlementDto);
  }

  @UseGuards(AccessTokenGuard)
  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.settlementsService.remove(id);
  }

  @UseGuards(AccessTokenGuard)
  @Patch(":id/status")
  changeStatus(
    @Param("id") id: string,
    @Body() changeStatusDto: ChangeStatusDto,
  ) {
    return this.settlementsService.changeStatus(id, changeStatusDto);
  }
}
