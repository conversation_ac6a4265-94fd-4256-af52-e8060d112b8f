import {
  BadGatewayException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Between, IsNull, Not, Repository } from "typeorm";
import { Settlement, SettlementStatus } from "./entities/settlement.entity";
import { CreateSettlementDto } from "./dto/create-settlement.dto";
import { UpdateSettlementDto } from "./dto/update-settlement.dto";
import { ChangeStatusDto } from "./dto/change-status.dto";
import { ResponseService } from "src/common/services/response.service";
import { paginate, PaginateQuery } from "nestjs-paginate";
import { WalletService, WalletUpdateResult } from "src/wallet/wallet.service";
import * as ExcelJS from "exceljs";

@Injectable()
export class SettlementsService {
  constructor(
    @InjectRepository(Settlement)
    private readonly settlementsRepository: Repository<Settlement>,
    private responseService: ResponseService,
    private readonly walletService: WalletService,
  ) {}

  // Function to generate a unique transaction ID
  private async generateTransactionId(): Promise<string> {
    const lastSettlement = await this.settlementsRepository.findOne({
      order: { created_at: "DESC" },
      select: ["transaction_id"],
      where: {
        id: Not(IsNull()),
      },
    });

    let lastId = 0;
    if (lastSettlement && lastSettlement.transaction_id) {
      lastId = parseInt(lastSettlement.transaction_id, 10);
    }

    const newId = (lastId + 1).toString().padStart(4, "0"); // Ensure it starts with 0001
    return newId;
  }

  async create(createSettlementDto: CreateSettlementDto, user_id: string) {
    let wallet: WalletUpdateResult = null;
    if (createSettlementDto.hospital_id) {
      wallet = await this.walletService.fetchAndUpdateHospitalWallet(
        createSettlementDto.hospital_id,
      );
    } else if (createSettlementDto.doctor_id) {
      wallet = await this.walletService.fetchAndUpdateHospitalWallet(
        createSettlementDto.doctor_id,
      );
    }

    if (!wallet) {
      throw new NotFoundException("No wallet");
    }

    if (
      Number(wallet.data.balance_amount) < Number(createSettlementDto.amount)
    ) {
      throw new BadGatewayException("Insufficient balance in wallet");
    }

    const settlement = this.settlementsRepository.create(createSettlementDto);
    settlement.status = SettlementStatus.REQUESTED;
    settlement.request_date = new Date();
    settlement.user_id = user_id;

    // Generate a unique transaction ID
    settlement.transaction_id = await this.generateTransactionId();

    await this.settlementsRepository.save(settlement);
    await this.walletService.updateBalanceAmount(
      wallet.data,
      createSettlementDto.amount,
    );
    return this.responseService.successResponse(
      "Settlement created successfully",
    );
  }

  async findAll(query: PaginateQuery, filters: any) {
    let where: any = {
      id: Not(IsNull()),
    };
    if (filters.status) {
      where.status = filters.status;
    }
    if (filters.hospital_id) {
      where.hospital_id = filters.hospital_id;
    }
    if (filters.doctor_id) {
      where.doctor_id = filters.doctor_id;
    }

    if (filters.from_date && filters.to_date) {
      const fromDate = new Date(filters.from_date);
      fromDate.setHours(0, 0, 0, 0); // Start of the day

      const toDate = new Date(filters.to_date);
      toDate.setHours(23, 59, 59, 999); // End of the day

      where.request_date = Between(fromDate, toDate);
    }
    return paginate(query, this.settlementsRepository, {
      sortableColumns: ["request_date"],
      relations: ["hospital", "user", "doctor"],
      where: where,
    });
  }

  async findAllAdmin(query: PaginateQuery, filters: any) {
    let where: any = {
      id: Not(IsNull()),
    };
    if (filters.hospital_id) {
      where.hospital_id = filters.hospital_id;
    }
    if (filters.status) {
      where.status = filters.status;
    }
    if (filters.doctor_id) {
      where.doctor_id = filters.doctor_id;
    }

    if (filters.from_date && filters.to_date) {
      const fromDate = new Date(filters.from_date);
      fromDate.setHours(0, 0, 0, 0); // Start of the day

      const toDate = new Date(filters.to_date);
      toDate.setHours(23, 59, 59, 999); // End of the day

      where.request_date = Between(fromDate, toDate);
    }
    return paginate(query, this.settlementsRepository, {
      sortableColumns: ["request_date"],
      relations: ["hospital", "user", "doctor"],
      where: where,
    });
  }

  async findOne(id: string) {
    const settlement = await this.settlementsRepository.findOne({
      where: { id },
      relations: ["hospital", "user", "doctor"],
    });
    if (!settlement) {
      throw new NotFoundException(`Settlement with ID ${id} not found`);
    }
    return {
      status: true,
      message: "Settlement found successfully",
      data: settlement,
    };
  }

  async update(id: string, updateSettlementDto: UpdateSettlementDto) {
    const result = await this.settlementsRepository.update(
      id,
      updateSettlementDto,
    );
    if (result.affected === 0) {
      throw new NotFoundException(`Settlement with ID ${id} not found`);
    }
    return this.findOne(id);
  }

  async remove(id: string) {
    const result = await this.settlementsRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Settlement with ID ${id} not found`);
    }
    return { message: "Settlement removed successfully" };
  }

  async changeStatus(id: string, changeStatusDto: ChangeStatusDto) {
    const settlement = await this.settlementsRepository.findOneBy({
      id,
    });
    if (!settlement) {
      throw new NotFoundException(`Settlement with ID ${id} not found`);
    }

    let wallet: WalletUpdateResult = null;
    if (settlement.hospital_id) {
      wallet = await this.walletService.fetchAndUpdateHospitalWallet(
        settlement.hospital_id,
      );
    } else if (settlement.doctor_id) {
      wallet = await this.walletService.fetchAndUpdateHospitalWallet(
        settlement.doctor_id,
      );
    }

    if (!wallet) {
      throw new NotFoundException("No wallet");
    }
    settlement.status = changeStatusDto.status;
    settlement.note = changeStatusDto.note;
    settlement.approve_date = new Date();
    await this.settlementsRepository.save(settlement);
    await this.walletService.updateWithdrawalAmount(
      wallet.data,
      settlement.amount,
      changeStatusDto.status,
    );
    return {
      status: true,
      message: "Settlement status updated successfully",
      data: settlement,
    };
  }

  async getLatestSettlementByHospital(hospital_id: string) {
    return await this.settlementsRepository.findOne({
      where: { hospital_id },
      order: { request_date: "DESC" },
    });
  }

  private async createExcelWorkbook(data: any[], type: "hospital" | "admin") {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Settlements");
    const columns = [
      { header: "Settlement ID", key: "id", width: 20 },
      { header: "Amount", key: "amount", width: 12 },
      { header: "Status", key: "status", width: 15 },
      { header: "Created At", key: "created_at", width: 20 },
      { header: "Updated At", key: "updated_at", width: 20 },
    ];
    if (type === "admin") {
      columns.push({
        header: "Hospital Name",
        key: "hospital_name",
        width: 20,
      });
    }
    worksheet.columns = columns;
    data.forEach((row) => worksheet.addRow(row));
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE0E0E0" },
    };
    worksheet.getColumn("amount").numFmt = "₹#,##0.00";
    return workbook;
  }

  async exportHospitalSettlements(filters: any) {
    const where = { hospital_id: filters.hospital_id, ...filters };

    if (filters.from_date && filters.to_date) {
      const fromDate = new Date(filters.from_date);
      fromDate.setHours(0, 0, 0, 0);
      const toDate = new Date(filters.to_date);
      toDate.setHours(23, 59, 59, 999);
      where.request_date = Between(fromDate, toDate);
      delete where.from_date;
      delete where.to_date;
    }
    const settlements = await this.settlementsRepository.find({
      where,
      order: { created_at: "DESC" },
    });
    const data = settlements.map((s) => ({
      id: s.id,
      amount: s.amount,
      status: s.status,
      created_at: s.created_at ? s.created_at.toISOString() : "N/A",
      updated_at: s.updated_at ? s.updated_at.toISOString() : "N/A",
    }));
    const workbook = await this.createExcelWorkbook(data, "hospital");
    const buffer = await workbook.xlsx.writeBuffer();
    return {
      buffer,
      filename: `hospital_settlements_${filters.hospital_id}.xlsx`,
    };
  }

  async exportAdminSettlements(filters: any) {
    if (filters.from_date && filters.to_date) {
      const fromDate = new Date(filters.from_date);
      fromDate.setHours(0, 0, 0, 0);
      const toDate = new Date(filters.to_date);
      toDate.setHours(23, 59, 59, 999);
      filters.request_date = Between(fromDate, toDate);
      delete filters.from_date;
      delete filters.to_date;
    }
    const settlements = await this.settlementsRepository.find({
      where: filters,
      relations: ["hospital"],
      order: { created_at: "DESC" },
    });
    const data = settlements.map((s) => ({
      id: s.id,
      amount: s.amount,
      status: s.status,
      created_at: s.created_at ? s.created_at.toISOString() : "N/A",
      updated_at: s.updated_at ? s.updated_at.toISOString() : "N/A",
      hospital_name: s.hospital?.name || "N/A",
    }));
    const workbook = await this.createExcelWorkbook(data, "admin");
    const buffer = await workbook.xlsx.writeBuffer();
    return {
      buffer,
      filename: `admin_settlements.xlsx`,
    };
  }
}
