import { Module } from "@nestjs/common";
import { SettlementsService } from "./settlements.service";
import { SettlementsController } from "./settlements.controller";
import { CommonModule } from "src/common/common.module";
import { UsersModule } from "src/users/users.module";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Settlement } from "./entities/settlement.entity";
import { WalletModule } from "src/wallet/wallet.module";

@Module({
  imports: [
    CommonModule,
    UsersModule,
    TypeOrmModule.forFeature([Settlement]),
    WalletModule,
  ],
  controllers: [SettlementsController],
  providers: [SettlementsService],
  exports: [SettlementsService],
})
export class SettlementsModule {}
