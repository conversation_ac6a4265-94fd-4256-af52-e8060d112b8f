import { IsIn, <PERSON>NotEmpty, <PERSON>Optional, IsString } from "class-validator";

export class CreateDepartmentDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsString()
  @IsNotEmpty()
  hospital_id: string;

  @IsOptional()
  @IsIn(["active", "inactive"])
  @IsString()
  status?: string;

  @IsOptional()
  @IsString()
  category?: string;
}
