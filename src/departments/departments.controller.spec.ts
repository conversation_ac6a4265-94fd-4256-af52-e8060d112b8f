import { Test, TestingModule } from "@nestjs/testing";
import { DepartmentsController } from "./departments.controller";
import { DepartmentsService } from "./departments.service";

describe("DepartmentsController", () => {
  let controller: DepartmentsController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DepartmentsController],
      providers: [DepartmentsService],
    }).compile();

    controller = module.get<DepartmentsController>(DepartmentsController);
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });
});
