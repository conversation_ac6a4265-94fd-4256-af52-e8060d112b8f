import { HttpStatus, Injectable, NotFoundException } from "@nestjs/common";
import { CreateDepartmentDto } from "./dto/create-department.dto";
import { UpdateDepartmentDto } from "./dto/update-department.dto";
import { InjectRepository } from "@nestjs/typeorm";
import { Department } from "./entities/department.entity";
import { IsNull, Like, Not, Repository } from "typeorm";
import { HospitalsService } from "src/hospitals/hospitals.service";

@Injectable()
export class DepartmentsService {
  constructor(
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    // private readonly hospitalsService: HospitalsService,
  ) {}

  async create(createDepartmentDto: CreateDepartmentDto): Promise<Department> {
    const department = this.departmentRepository.create(createDepartmentDto);
    return this.departmentRepository.save(department);
  }

  async findAll(): Promise<Department[]> {
    return this.departmentRepository.find();
  }

  async findOne(id: string): Promise<Department> {
    const department = await this.departmentRepository.findOne({
      where: { id },
    });

    if (!department) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }

    return department;
  }

  async findByHospitalId(filter: any): Promise<Department[]> {
    let where: any = {
      hospital_id: filter.hospital_id,
    };
    if (filter.name) {
      where.name = Like(`%${filter.name}%`);
    }
    return await this.departmentRepository.find({
      where: where,
    });
  }

  async update(
    id: string,
    updateDepartmentDto: UpdateDepartmentDto,
  ): Promise<Department> {
    const department = await this.findOne(id);
    Object.assign(department, updateDepartmentDto);
    return this.departmentRepository.save(department);
  }

  async remove(id: string) {
    const result = await this.departmentRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }
    return {
      statusCode: HttpStatus.OK,
      message: "Department deleted successfully",
    };
  }
}
