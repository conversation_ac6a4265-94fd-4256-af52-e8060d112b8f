import { Hospital } from "src/hospitals/entities/hospital.entity";
import { User } from "src/users/entities/user.entity";
import {
  Column,
  Entity,
  JoinColumn,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
} from "typeorm";

@Entity({ name: "departments" })
export class Department {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 255 })
  name: string;

  @Column({ type: "varchar", length: 255 })
  hospital_id: string;

  @Column()
  status: string;

  @Column()
  category: string;

  @Column({ type: "text", nullable: true })
  description: string;

  @ManyToOne(() => Hospital, (hospital) => hospital.departments, {
    nullable: false,
  })
  @JoinColumn({ name: "hospital_id" })
  hospital: Hospital;

  @ManyToMany(() => User, (user) => user.departments)
  users: User[];
}
