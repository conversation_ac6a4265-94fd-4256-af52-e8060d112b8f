import {
  Is<PERSON>tring,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  IsEmail,
  IsBoolean,
  Is<PERSON><PERSON>D,
  <PERSON>Int,
  Min,
  IsNotEmpty,
  IsJSON,
  IsObject,
  Is<PERSON>rray,
} from "class-validator";
import { UserRoles } from "src/types/users.types";
import { Gender } from "../entities/user.entity";
import { <PERSON><PERSON><PERSON><PERSON> } from "src/types/doctors.type";

export class CreateUserDto {
  @IsString()
  first_name?: string;

  @IsString()
  last_name?: string;

  @IsNotEmpty()
  @IsEnum(["superAdmin", "client", "user", "staff", "hsAdmin"])
  user_type?: string;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @IsString()
  password?: string;

  @IsOptional()
  @IsString()
  job_title?: string;

  @IsObject()
  @IsOptional()
  address?: <PERSON><PERSON>dd<PERSON>;

  @IsOptional()
  @IsBoolean()
  is_active?: boolean;

  @IsString()
  role: User<PERSON><PERSON>s;

  @IsNotEmpty()
  @IsUUID()
  hospital_id?: string;

  @IsOptional()
  @IsEnum(Gender)
  gender?: Gender;

  @IsOptional()
  @IsInt()
  @Min(1)
  age?: number;

  @IsOptional()
  district_id?: string;

  @IsOptional()
  department_id?: string;

  @IsOptional()
  profile_picture?: string | null;

  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  department_ids?: string[];

  @IsOptional()
  otp_secret_forgot_password?: string;
}
