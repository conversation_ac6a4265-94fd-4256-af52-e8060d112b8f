import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { CreateUserDto } from "./dto/create-user.dto";
import { UpdateUserDto } from "./dto/update-user.dto";
import { InjectRepository } from "@nestjs/typeorm";
import { User } from "./entities/user.entity";
import { In, IsNull, Like, Not, Repository } from "typeorm";
import { RolesService } from "src/roles/roles.service";
import { DoctorSpecialisationService } from "src/doctor_specialisation/doctor_specialisation.service";
import { DistrictsService } from "src/districts/districts.service";
import { ResponseService } from "src/common/services/response.service";
import { UserDepartmentsService } from "src/user_departments/user_departments.service";
import { paginate, Paginated, PaginateQuery } from "nestjs-paginate";
import { ChangePasswordDto } from "./dto/change-password.dto";

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private readonly doctorSpecialisationService: DoctorSpecialisationService,
    private readonly roleService: RolesService,
    private readonly districtService: DistrictsService,
    private readonly userDepartmentService: UserDepartmentsService,
  ) { }

  async toggleActiveStatus(id: string): Promise<any> {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException("User not found");
    }

    user.is_active = !user.is_active;
    await this.userRepository.save(user);

    return {
      status: true,
      message: `User ${user.is_active ? "activated" : "deactivated"} successfully`,
      data: { is_active: user.is_active },
    };
  }

  async create(createUserDto: CreateUserDto): Promise<User> {
    const user = this.userRepository.create(createUserDto);
    return await this.userRepository.save(user);
  }

  async findAll(query: PaginateQuery, filters: any): Promise<Paginated<User>> {
    let where: any = {
      id: Not(IsNull()),
    };

    // Filter by name
    if (filters.name) {
      where.first_name = Like(`%${filters.name}%`);
    }

    // Filter by hospital ID
    if (filters.hospital_id) {
      where.hospital_id = filters.hospital_id;
    }
    if (filters.user_type) {
      // Filter by doctor type
      where.user_type = In((filters.user_type).split(","));
    }
    if (filters.gender) {
      // Filter by doctor type
      where.gender = filters.gender;
    }

    return paginate(query, this.userRepository, {
      sortableColumns: ["created_at"],
      relations: ["departments"],
      where: where,
    });
  }

  async findOne(id: string) {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ["departments"],
    });
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    return {
      status: true,
      message: "User found successfully",
      data: user,
    };
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<any> {
    const { department_ids, ...data } = updateUserDto;
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException("User not found");
    }

    if (department_ids) {
      await this.userDepartmentService.deleteUserDepartment(user.id);
      department_ids.forEach(async (element) => {
        await this.userDepartmentService.addUserToDepartment({
          user_id: user.id,
          department_id: element,
        });
      });
    }
    await this.userRepository.update(id, data);
    return {
      status: true,
      message: "User updated successfully",
    };
  }

  async remove(id: string): Promise<any> {
    await this.userRepository.delete(id);
    return {
      status: true,
      message: "User deleted successfully",
    };
  }

  async saveOtpSecret(userId: string, otpSecret: string): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id: userId } });

    if (!user) {
      throw new NotFoundException("User not found");
    }

    user.otp_secret = otpSecret;
    return await this.userRepository.save(user);
  }

  async findOneByParam(params: {
    [key: string]: any;
  }): Promise<User | undefined> {
    return this.userRepository.findOneBy(params);
  }

  async addRoleToUser(user: User, roleName: string): Promise<User> {
    const role = await this.roleService.findOneByName(roleName);
    if (!user.roles) {
      user.roles = [];
    }
    user.roles.push(role);
    return await this.userRepository.save(user);
  }

  async addSpecialisationToUser(
    user_id: string,
    specialisations_id: string[],
  ): Promise<void> {
    if (!specialisations_id.length) {
      throw new BadRequestException(`Specialisation Id cannot be empty array`);
    }

    const specialisations = specialisations_id.map((id) => ({
      user_id,
      specialisation_id: id,
    }));

    await this.doctorSpecialisationService.addSpecialisationToUsers(
      specialisations,
    );
  }

  async saveInstance(user: User) {
    return await this.userRepository.save(user);
  }

  async createStaffUser(dto: CreateUserDto) {
    const { email, phone, user_type, department_ids } = dto;
    const isUserExistEmail = email
      ? await this.userRepository.findOne({
        where: [{ email }],
      })
      : null;
    if (isUserExistEmail) {
      throw new BadRequestException(`User with email ${email} already exists`);
    }
    const isUserExistPhone = await this.userRepository.findOne({
      where: [{ phone }],
    });
    if (isUserExistPhone) {
      throw new BadRequestException(`User with phone ${phone} already exists`);
    }
    const user = await this.create(dto);
    const role = await this.roleService.findOneByName(user_type);
    await this.addRoleToUser(user, role.name);
    if (department_ids) {
      department_ids.forEach(async (element) => {
        await this.userDepartmentService.addUserToDepartment({
          user_id: user.id,
          department_id: element,
        });
      });
    }
    return {
      message: "User created successfully",
      status: true,
      data: user,
    };
  }

  async changePassword(id: string, dto: ChangePasswordDto) {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException("User not found");
    }
    if (dto.password !== dto.confirm_password) {
      throw new BadRequestException(
        "New password and confirm password do not match",
      );
    }
    await this.userRepository.update(id, { password: dto.password });
    return {
      status: true,
      message: "User updated successfully",
    };
  }

  async updateDistrictId(hospital_id: string, district_id: string) {
    const doctors = await this.userRepository.update(hospital_id, {
      district_id: district_id,
    });
  }
}
