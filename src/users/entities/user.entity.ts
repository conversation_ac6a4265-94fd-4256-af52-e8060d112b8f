import { Role } from "src/roles/entities/role.entity";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  PrimaryColumn,
  Generated,
  ManyToMany,
  JoinTable,
  BeforeInsert,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from "typeorm";
import * as bcrypt from "bcrypt";
import { District } from "src/districts/entities/districts.entity";
import { DoctorAddress } from "src/types/doctors.type";
import { Department } from "src/departments/entities/department.entity";
import { Review } from "src/reviews/entities/review.entity";
import { Transaction } from "src/transactions/entities/transaction.entity";
import { Wallet } from "src/wallet/entities/wallet.entity";

export enum Gender {
  MALE = "male",
  FEMALE = "female",
  OTHER = "other",
}
@Entity("users")
export class User {
  @PrimaryColumn("varchar", { scale: 36 })
  @Generated("uuid")
  id: string;

  @Column("varchar", { nullable: true })
  first_name?: string;

  @Column("varchar", { nullable: true })
  last_name?: string;

  @Column("enum", {
    enum: ["superAdmin", "client", "user", "staff"],
    nullable: true,
  })
  user_type: string;

  @Column("varchar", { unique: true, nullable: true })
  email: string;

  @Column("varchar", { nullable: true })
  phone: string;

  @Column("varchar", { nullable: true })
  password: string;

  @Column("boolean", { default: true })
  is_active: boolean;

  @Column("varchar", { nullable: true, default: null })
  hospital_id: string;

  @Column("uuid", { nullable: true })
  district_id: string | null;

  @Column({ nullable: true })
  job_title: string | null;

  @Column({ nullable: true })
  profile_picture: string | null;

  @Column("varchar", { nullable: true, default: null })
  otp_secret?: string;

  @Column("varchar", { nullable: true, default: null })
  otp_secret_forgot_password?: string;

  @Column("simple-json", { nullable: true })
  address: DoctorAddress;

  @CreateDateColumn({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  created_at: Date;

  @UpdateDateColumn({
    type: "timestamp",
    default: () => "CURRENT_TIMESTAMP",
    onUpdate: "CURRENT_TIMESTAMP",
  })
  updated_at: Date;

  @DeleteDateColumn({ type: "timestamp", nullable: true })
  deleted_at: Date;

  @BeforeInsert() async hashPassword() {
    if (this.password) {
      const saltOrRounds = 10;
      this.password = await bcrypt.hash(this.password, saltOrRounds);
    }
  }

  // @BeforeUpdate() async hashPasswordOnUpdate() {
  //     const saltOrRounds = 10;
  //     this.password = await bcrypt.hash(this.password, saltOrRounds);
  // }

  async validatePassword(password: string): Promise<boolean> {
    return bcrypt.compare(password, this.password);
  }

  // async setPassword(newPassword: string): Promise<void> {
  //   const saltOrRounds = 10;
  //   this.password = await bcrypt.hash(newPassword, saltOrRounds);
  // }

  /*
   *
   * relationships
   */
  @ManyToMany(() => Role)
  @JoinTable({
    name: "role_user",
    joinColumn: {
      name: "user_id",
    },
    inverseJoinColumn: {
      name: "role_id",
    },
  })
  roles: Role[];

  @ManyToOne(() => District, (district) => district.users, { nullable: true })
  @JoinColumn({ name: "district_id" }) // Ensure the column is properly named
  district?: District;

  @Column({
    type: "enum",
    enum: Gender,
    nullable: true,
  })
  gender?: Gender;

  @Column({ type: "int", nullable: true })
  age?: number;

  @ManyToMany(() => Department)
  @JoinTable({
    name: "user_departments",
    joinColumn: {
      name: "user_id",
    },
    inverseJoinColumn: {
      name: "department_id",
    },
  })
  departments: Department[];

  @OneToMany(() => Review, (review) => review.user)
  reviews: Review[];

  @OneToMany(() => Transaction, (review) => review.user)
  transactions: Transaction[];
}
