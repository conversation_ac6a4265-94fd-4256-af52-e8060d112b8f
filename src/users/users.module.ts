import { Modu<PERSON> } from "@nestjs/common";
import { UsersService } from "./users.service";
import { UsersController } from "./users.controller";
import { TypeOrmModule } from "@nestjs/typeorm";
import { User } from "./entities/user.entity";
import { RolesModule } from "src/roles/roles.module";
import { DoctorSpecialisationModule } from "src/doctor_specialisation/doctor_specialisation.module";
import { DistrictsModule } from "src/districts/districts.module";
import { CommonModule } from "src/common/common.module";
import { UserDepartmentsModule } from "src/user_departments/user_departments.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    RolesModule,
    DoctorSpecialisationModule,
    DistrictsModule,
    UserDepartmentsModule,
  ],
  controllers: [UsersController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
