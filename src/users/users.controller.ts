import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  BadRequestException,
} from "@nestjs/common";
import { UsersService } from "./users.service";
import { CreateUserDto } from "./dto/create-user.dto";
import { UpdateUserDto } from "./dto/update-user.dto";
import { Paginate, PaginateQuery } from "nestjs-paginate";
import { ChangePasswordDto } from "./dto/change-password.dto";

@Controller("users")
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Post()
  create(@Body() createUserDto: CreateUserDto) {
    return this.usersService.createStaffUser(createUserDto);
  }

  @Get()
  async findAll(@Paginate() query: PaginateQuery, @Query() filters: any) {
    if (!filters.hospital_id) {
      throw new BadRequestException("hospital_id is required");
    }
    return await this.usersService.findAll(query, filters);
  }

  @Get("admin")
  async findAllAdmin(@Paginate() query: PaginateQuery, @Query() filters: any) {
    return await this.usersService.findAll(query, filters);
  }

  @Get(":id")
  async findOne(@Param("id") id: string) {
    return await this.usersService.findOne(id);
  }

  @Patch(":id")
  update(@Param("id") id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.usersService.update(id, updateUserDto);
  }

  @Patch(":id/update")
  async updateUser(
    @Param("id") id: string,
    @Body() updateUserDto: UpdateUserDto,
  ) {
    return this.usersService.update(id, updateUserDto);
  }

  @Patch(":id/toggle-active")
  async toggleActiveStatus(@Param("id") id: string) {
    return this.usersService.toggleActiveStatus(id);
  }

  @Patch(":id/change-password")
  async changePassword(
    @Param("id") id: string,
    @Body() ChangePasswordDto: ChangePasswordDto,
  ) {
    return this.usersService.changePassword(id, ChangePasswordDto);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.usersService.remove(id);
  }
}
