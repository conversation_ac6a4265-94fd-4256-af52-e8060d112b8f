import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  UseGuards,
} from "@nestjs/common";
import { NotificationsService } from "./notifications.service";
import { CreateNotificationDto } from "./dto/create-notification.dto";
import { UpdateNotificationDto } from "./dto/update-notification.dto";
import { Paginate, PaginateQuery } from "nestjs-paginate";
import { Request } from "express";
import { AccessTokenGuard } from "src/common/guards/accessToken.guard";

@Controller("notifications")
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Post()
  async create(@Body() createNotificationDto: CreateNotificationDto) {
    await this.notificationsService.send(createNotificationDto);
    return {
      status: true,
      message: "Notification send successfully",
    };
  }

  @Get()
  @UseGuards(AccessTokenGuard)
  findAll(@Paginate() query: PaginateQuery, @Req() req: Request) {
    return this.notificationsService.findAll(query, req["uid"]);
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.notificationsService.findOne(id);
  }

  @Patch(":id")
  update(
    @Param("id") id: string,
    @Body() updateNotificationDto: UpdateNotificationDto,
  ) {
    return this.notificationsService.update(id, updateNotificationDto);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.notificationsService.remove(id);
  }
}
