import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as AWS from 'aws-sdk';
import * as fs from 'fs';
import * as path from 'path';

export interface EmailTemplateData {
  [key: string]: string | number;
}

export interface SendEmailOptions {
  to: string | string[];
  subject: string;
  templateName: string;
  templateData: EmailTemplateData;
}

@Injectable()
export class EmailService {
  private ses: AWS.SES;
  private readonly logger = new Logger(EmailService.name);
  private readonly senderEmail: string;

  constructor(private configService: ConfigService) {
    // Configure AWS SES
    AWS.config.update({
      accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID'),
      secretAccessKey: this.configService.get<string>('AWS_SECRET_ACCESS_KEY'),
      region: this.configService.get<string>('AWS_REGION', 'us-east-1'),
    });

    this.ses = new AWS.SES();
    this.senderEmail = this.configService.get<string>('SES_SENDER_EMAIL');

    if (!this.senderEmail) {
      this.logger.warn('SES_SENDER_EMAIL not configured in environment variables');
    }
  }

  /**
   * Send email using AWS SES with HTML template
   */
  async sendEmail(options: SendEmailOptions): Promise<boolean> {
    try {
      const { to, subject, templateName, templateData } = options;
      
      // Load and process template
      const htmlContent = await this.loadAndProcessTemplate(templateName, templateData);
      
      // Prepare recipients
      const recipients = Array.isArray(to) ? to : [to];
      
      // SES email parameters
      const params: AWS.SES.SendEmailRequest = {
        Source: this.senderEmail,
        Destination: {
          ToAddresses: recipients,
        },
        Message: {
          Subject: {
            Data: subject,
            Charset: 'UTF-8',
          },
          Body: {
            Html: {
              Data: htmlContent,
              Charset: 'UTF-8',
            },
          },
        },
      };

      // Send email
      const result = await this.ses.sendEmail(params).promise();
      this.logger.log(`Email sent successfully. MessageId: ${result.MessageId}`);
      return true;
    } catch (error) {
      this.logger.error('Failed to send email:', error);
      throw new Error(`Failed to send email: ${error.message}`);
    }
  }

  /**
   * Load HTML template and replace placeholders with actual data
   */
  private async loadAndProcessTemplate(
    templateName: string,
    templateData: EmailTemplateData,
  ): Promise<string> {
    try {
      // Construct template file path
      const templatePath = path.join(
        process.cwd(),
        'src',
        'notifications',
        'templates',
        `${templateName}.html`,
      );

      // Check if template file exists
      if (!fs.existsSync(templatePath)) {
        throw new Error(`Email template '${templateName}' not found at ${templatePath}`);
      }

      // Read template file
      let htmlContent = fs.readFileSync(templatePath, 'utf-8');

      // Replace placeholders with actual data
      htmlContent = this.replacePlaceholders(htmlContent, templateData);

      return htmlContent;
    } catch (error) {
      this.logger.error(`Failed to load template '${templateName}':`, error);
      throw error;
    }
  }

  /**
   * Replace placeholders in template with actual values
   * Placeholders should be in format: {{placeholder_name}}
   */
  private replacePlaceholders(
    template: string,
    data: EmailTemplateData,
  ): string {
    let processedTemplate = template;

    // Replace each placeholder with corresponding data
    Object.keys(data).forEach((key) => {
      const placeholder = `{{${key}}}`;
      const value = String(data[key]);
      processedTemplate = processedTemplate.replace(new RegExp(placeholder, 'g'), value);
    });

    // Log warning for unreplaced placeholders
    const unreplacedPlaceholders = processedTemplate.match(/\{\{[^}]+\}\}/g);
    if (unreplacedPlaceholders) {
      this.logger.warn(
        `Unreplaced placeholders found: ${unreplacedPlaceholders.join(', ')}`,
      );
    }

    return processedTemplate;
  }

  /**
   * Verify email address with SES (useful for development)
   */
  async verifyEmailAddress(email: string): Promise<boolean> {
    try {
      const params = {
        EmailAddress: email,
      };
      await this.ses.verifyEmailIdentity(params).promise();
      this.logger.log(`Email verification sent to: ${email}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to verify email ${email}:`, error);
      return false;
    }
  }

  /**
   * Get list of verified email addresses
   */
  async getVerifiedEmails(): Promise<string[]> {
    try {
      const result = await this.ses.listVerifiedEmailAddresses().promise();
      return result.VerifiedEmailAddresses || [];
    } catch (error) {
      this.logger.error('Failed to get verified emails:', error);
      return [];
    }
  }
}
