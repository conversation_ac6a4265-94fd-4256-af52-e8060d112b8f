import { Injectable } from "@nestjs/common";
import axios from "axios";

@Injectable()
export class SmsService {
  async sendOtpSms(
    phoneNumber: string,
    otpValue: string,
    templateName?: string,
  ): Promise<any> {
    const apiKey = process.env.TWO_FACTOR_API_KEY;
    if (apiKey) {
      const template =
        templateName || process.env.TWO_FACTOR_TEMPLATE_NAME || "LOGIN";
      const url = `https://2factor.in/API/V1/${apiKey}/SMS/${phoneNumber}/${otpValue}/${template}`;

      try {
        const response = await axios.get(url);
        console.log(url);
        console.log(response);
        return response.data;
      } catch (error) {
        console.error(`Failed to send SMS: ${error.message}`);
        // throw new Error(`Failed to send SMS: ${error.message}`);
      }
    }
  }
}
