import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { EmailService } from './email.service';
import * as AWS from 'aws-sdk';
import * as fs from 'fs';

// Mock AWS SES
jest.mock('aws-sdk');
jest.mock('fs');

describe('EmailService', () => {
  let service: EmailService;
  let configService: ConfigService;
  let mockSES: jest.Mocked<AWS.SES>;

  beforeEach(async () => {
    // Create mock SES instance
    mockSES = {
      sendEmail: jest.fn().mockReturnValue({
        promise: jest.fn().mockResolvedValue({ MessageId: 'test-message-id' }),
      }),
      verifyEmailIdentity: jest.fn().mockReturnValue({
        promise: jest.fn().mockResolvedValue({}),
      }),
      listVerifiedEmailAddresses: jest.fn().mockReturnValue({
        promise: jest.fn().mockResolvedValue({
          VerifiedEmailAddresses: ['<EMAIL>'],
        }),
      }),
    } as any;

    // Mock AWS.SES constructor
    (AWS.SES as jest.MockedClass<typeof AWS.SES>).mockImplementation(() => mockSES);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              switch (key) {
                case 'AWS_ACCESS_KEY_ID':
                  return 'test-access-key';
                case 'AWS_SECRET_ACCESS_KEY':
                  return 'test-secret-key';
                case 'AWS_REGION':
                  return 'us-east-1';
                case 'SES_SENDER_EMAIL':
                  return '<EMAIL>';
                default:
                  return undefined;
              }
            }),
          },
        },
      ],
    }).compile();

    service = module.get<EmailService>(EmailService);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendEmail', () => {
    it('should send email successfully with template', async () => {
      // Mock fs.existsSync and fs.readFileSync
      (fs.existsSync as jest.Mock).mockReturnValue(true);
      (fs.readFileSync as jest.Mock).mockReturnValue(
        '<html><body>Hello {{user_name}}, your OTP is {{otp}}</body></html>',
      );

      const emailOptions = {
        to: '<EMAIL>',
        subject: 'Test Email',
        templateName: 'test-template',
        templateData: {
          user_name: 'John Doe',
          otp: '123456',
        },
      };

      const result = await service.sendEmail(emailOptions);

      expect(result).toBe(true);
      expect(mockSES.sendEmail).toHaveBeenCalledWith({
        Source: '<EMAIL>',
        Destination: {
          ToAddresses: ['<EMAIL>'],
        },
        Message: {
          Subject: {
            Data: 'Test Email',
            Charset: 'UTF-8',
          },
          Body: {
            Html: {
              Data: '<html><body>Hello John Doe, your OTP is 123456</body></html>',
              Charset: 'UTF-8',
            },
          },
        },
      });
    });

    it('should handle multiple recipients', async () => {
      (fs.existsSync as jest.Mock).mockReturnValue(true);
      (fs.readFileSync as jest.Mock).mockReturnValue('<html><body>Test</body></html>');

      const emailOptions = {
        to: ['<EMAIL>', '<EMAIL>'],
        subject: 'Test Email',
        templateName: 'test-template',
        templateData: {},
      };

      await service.sendEmail(emailOptions);

      expect(mockSES.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          Destination: {
            ToAddresses: ['<EMAIL>', '<EMAIL>'],
          },
        }),
      );
    });

    it('should throw error when template not found', async () => {
      (fs.existsSync as jest.Mock).mockReturnValue(false);

      const emailOptions = {
        to: '<EMAIL>',
        subject: 'Test Email',
        templateName: 'non-existent-template',
        templateData: {},
      };

      await expect(service.sendEmail(emailOptions)).rejects.toThrow(
        "Email template 'non-existent-template' not found",
      );
    });

    it('should throw error when SES fails', async () => {
      (fs.existsSync as jest.Mock).mockReturnValue(true);
      (fs.readFileSync as jest.Mock).mockReturnValue('<html><body>Test</body></html>');
      
      mockSES.sendEmail.mockReturnValue({
        promise: jest.fn().mockRejectedValue(new Error('SES Error')),
      } as any);

      const emailOptions = {
        to: '<EMAIL>',
        subject: 'Test Email',
        templateName: 'test-template',
        templateData: {},
      };

      await expect(service.sendEmail(emailOptions)).rejects.toThrow('Failed to send email: SES Error');
    });
  });

  describe('verifyEmailAddress', () => {
    it('should verify email address successfully', async () => {
      const result = await service.verifyEmailAddress('<EMAIL>');

      expect(result).toBe(true);
      expect(mockSES.verifyEmailIdentity).toHaveBeenCalledWith({
        EmailAddress: '<EMAIL>',
      });
    });

    it('should handle verification failure', async () => {
      mockSES.verifyEmailIdentity.mockReturnValue({
        promise: jest.fn().mockRejectedValue(new Error('Verification failed')),
      } as any);

      const result = await service.verifyEmailAddress('<EMAIL>');

      expect(result).toBe(false);
    });
  });

  describe('getVerifiedEmails', () => {
    it('should return list of verified emails', async () => {
      const result = await service.getVerifiedEmails();

      expect(result).toEqual(['<EMAIL>']);
      expect(mockSES.listVerifiedEmailAddresses).toHaveBeenCalled();
    });

    it('should handle empty list', async () => {
      mockSES.listVerifiedEmailAddresses.mockReturnValue({
        promise: jest.fn().mockResolvedValue({ VerifiedEmailAddresses: [] }),
      } as any);

      const result = await service.getVerifiedEmails();

      expect(result).toEqual([]);
    });
  });
});
