/** @format */

import { BadRequestException, Injectable } from "@nestjs/common";
import { NotificationInterface } from "../interface/notification.interface";
const credentials = {
  type: "service_account",
  project_id: "zodo-ai",
  private_key_id: "5892909f30a9cd1d9de5a330e28fce1319263898",
  private_key:
    "-----B<PERSON>IN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCrrGmsurFHFyb4\nygTGtFnFmCrcQzFu3ukkE/1oR1w5vmMwLoGv5h3iGtbC+CtbqfUolfdk+ZIOGa+r\nv/6XZ0zTUy+5vn4GmxMkULDG8ujgvxbHbeNPEo+1LHxcvWUkePzJe4RRjfM4gUvp\nI1govuPUT+ebTJdkOaeesS10Nsue7IY8sN0P59ZxiXAQzQaDTynz1FliQzN2oSkW\nmGpTLIoKdUt0kW2eb2i8N9ySySL3lD5U0xAtR0KqZg1l3mqN4JAMogo16XHgXBP9\nFPAPnIvn/e2t/TugXrfmkMMFge+wxtBD3S+0L9lL7yzoEEp1B2BXY/HQnil4xSvK\nys8vVWxXAgMBAAECggEADIrqczbcqh+atridarfu8Y0LLJz+j2XBgOPlzGjSTltT\noeFaX+cFlE+qFNECHsHAtxHMSaTv5kNukbM58m19fNi23VcTaOYkzhVuWDEtKvNE\nwpaLvtFrWhOBYcE3Er6ByzK3P/VWdnCaT2x7dD0MjKZRFKHSIDETxxHI18g2tLCs\nMxrI1TiZIc7TkM8yF1CIW8uf7nSnrMk2R7pYLiyJrqz6D3ou/e0cvBUrUquivuPk\n7s+LmIsCwZ7r7QNy74XXDTSk9218Fl0u1ADocpiB7Cgg1QhQqeTcDHEKjbboFVFX\ngkAeaMXY3Azfl9Tt6/JWkJqRBW5eqpR5YDl/BTe/0QKBgQDV/LAeadmJmeAAdOaJ\nufgJ8/V8qUEWvu5Lv1I+cnRlddAXTCXFp1+b6uGiHy/LFD8tFVHuefozfv1Ae+1d\n/wUYwpgtbc9wSeS/iRkxPjodAE47BnlKLpCI5by9Wm/tA5S3tZH3z3Q+HLfcGTyn\n75+zYYcv+veIvkpgSxRSUmHUWQKBgQDNYPsXa9d9l14KlzrGTiq8ESa5Bnq7fruQ\nrPdTkwzRP+6+GtPSuyS+ivt6GAmWHUD+1wI6MXeBRbs0BM27oqE+hJPK1cOyfUfM\n1cYbVqc29MAZ+MYjWRvZfzmNT4vQYuOWOwQABJWJnFdcMLbR4Eq5TZzE6g2z5JS9\nBqotgSzwLwKBgGUiXiKhqFyuSv4sQeEJ8b7SfE2EuOyFJJ7MN5SoB3cEbWr1ZEy8\nRCWupyE2WCJE2W73SpfJMoUwGYVmjNMF+mSvY7jk00ugwOn9Y9iwLksSn+apsp1q\n0IjC+6cKnSeJr7n7qJdUhv3sGMF/c/ly+SmQiJIWrPMF7KUCFwKpSO9RAoGAAqEW\nNdOMm6//ZTVQbE993pB9pWq+0X2q2wxL0kcUp+0gmlBvm8gHjWTsGUELhl9zgI+y\nGJqfhAknf1/8XkWTuOAroDIiYjuWWrn14k22sE5r0ylLKBKw95TYYMW+7XkGntq8\nj19/ppZYy/ZXsNIlQsAKnL6+VCo9+TmHnjOEJsMCgYEAm+Lib9F96N3AXnt+wZMW\nBl3xJmS7N3x509wwxZuV/CQJ2Wy5c2Cac/hh9Uh2VNJqgmaXxJxXUq0wf/JlpuKs\nGFLt1iFplP/ey9lNFjUirPGlxcrFwjji4fo7rths7w2q0wIFEPj1S2GtatMZDVKO\n8IbZAIuK+pWTt6leVrXkIuY=\n-----END PRIVATE KEY-----\n",
  client_email: "<EMAIL>",
  client_id: "101803529320929752306",
  auth_uri: "https://accounts.google.com/o/oauth2/auth",
  token_uri: "https://oauth2.googleapis.com/token",
  auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
  client_x509_cert_url:
    "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40zodo-ai.iam.gserviceaccount.com",
  universe_domain: "googleapis.com",
};

@Injectable()
export class FirebaseService {
  async sendPushNotification(
    tokens: string[],
    notification: NotificationInterface,
  ) {
    console.log("sendPushNotification");
    const firebase = require("firebase-admin");
    if (!firebase.apps.length) {
      firebase.initializeApp({
        credential: firebase.credential.cert(credentials),
      });
    }
    tokens.forEach(async (token) => {
      const message = {
        token,
        notification,
      };
      try {
        const response = await firebase.messaging().send(message);
        console.log("push sent successfully!");
        console.log(response);
      } catch (error) {
        console.error("Error sending email:", error);
      }
    });
  }
}
