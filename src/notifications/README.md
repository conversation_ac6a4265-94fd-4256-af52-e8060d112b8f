# Email Notifications with AWS SES

This module provides email notification functionality using AWS SES with HTML templates and placeholder replacement.

## Setup

### 1. Environment Variables

Add the following environment variables to your `.env` file:

```env
# AWS SES Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1
SES_SENDER_EMAIL=<EMAIL>
```

### 2. AWS SES Setup

1. **Verify your sender email address** in AWS SES console
2. **Request production access** if you want to send emails to unverified addresses
3. **Configure your domain** (optional but recommended for production)

## Usage

### Basic Email Sending

```typescript
import { NotificationsService } from './notifications.service';

// Inject the service in your controller/service
constructor(private notificationsService: NotificationsService) {}

// Send email with template
await this.notificationsService.sendEmailNotification(
  '<EMAIL>',                    // recipient(s)
  'Password Reset Request',              // subject
  'forgot-password',                     // template name
  {                                      // template data
    user_name: '<PERSON>',
    email: '<EMAIL>',
    otp: '123456',
    expiry_minutes: '10',
    app_name: 'Your App Name',
    current_year: '2025',
    sent_date: '07/27/2025',
    sent_time: '4:30 PM',
    app_url: 'https://yourapp.com'
  }
);
```

### Multiple Recipients

```typescript
await this.notificationsService.sendEmailNotification(
  ['<EMAIL>', '<EMAIL>'],  // multiple recipients
  'Welcome to Our Platform',
  'welcome',
  {
    user_name: 'New User',
    app_name: 'Your App Name',
    app_url: 'https://yourapp.com',
    current_year: '2025'
  }
);
```

## Email Templates

Templates are stored in `src/notifications/templates/` directory as HTML files.

### Template Structure

Templates use `{{placeholder_name}}` syntax for dynamic content replacement.

Example template (`forgot-password.html`):
```html
<!DOCTYPE html>
<html>
<head>
    <title>Password Reset</title>
</head>
<body>
    <h1>Hello {{user_name}}</h1>
    <p>Your OTP is: <strong>{{otp}}</strong></p>
    <p>This code expires in {{expiry_minutes}} minutes.</p>
</body>
</html>
```

### Available Templates

1. **forgot-password.html** - Password reset with OTP
   - Required placeholders: `user_name`, `email`, `otp`, `expiry_minutes`, `app_name`
   - Optional: `current_year`, `sent_date`, `sent_time`, `app_url`

2. **welcome.html** - Welcome email for new users
   - Required placeholders: `user_name`, `app_name`
   - Optional: `app_url`, `current_year`

### Creating New Templates

1. Create a new HTML file in `src/notifications/templates/`
2. Use `{{placeholder_name}}` for dynamic content
3. Include proper HTML structure with CSS styling
4. Test with your template data

## Integration Examples

### Forgot Password Flow

```typescript
// In auth.service.ts
async forgotPasswordRequest(email: string) {
  const user = await this.usersService.findOneByParam({ email });
  
  // Generate OTP
  const secret = await this.otpService.generateSecret();
  const otp = this.otpService.generateOtp(secret);
  
  // Save OTP secret
  user.otp_secret_forgot_password = secret;
  await this.usersService.saveInstance(user);

  // Send email notification
  const currentDate = new Date();
  await this.notificationsService.sendEmailNotification(
    user.email,
    'Password Reset Request - OTP Verification',
    'forgot-password',
    {
      user_name: user.first_name || user.email.split('@')[0],
      email: user.email,
      otp: otp,
      expiry_minutes: '10',
      app_name: 'Your App Name',
      current_year: currentDate.getFullYear().toString(),
      sent_date: currentDate.toLocaleDateString(),
      sent_time: currentDate.toLocaleTimeString(),
    }
  );

  return { success: true, message: 'OTP sent successfully' };
}
```

### Welcome Email

```typescript
// In user registration
async registerUser(userData: CreateUserDto) {
  const user = await this.usersService.create(userData);
  
  // Send welcome email
  await this.notificationsService.sendEmailNotification(
    user.email,
    'Welcome to Our Platform!',
    'welcome',
    {
      user_name: user.first_name || 'User',
      app_name: 'Your App Name',
      app_url: 'https://yourapp.com',
      current_year: new Date().getFullYear().toString(),
    }
  );

  return user;
}
```

## Error Handling

The email service includes comprehensive error handling:

- **Template not found**: Throws error if template file doesn't exist
- **SES errors**: Logs and throws SES-specific errors
- **Invalid configuration**: Warns about missing environment variables

## Testing

Run the email service tests:

```bash
npm test -- src/notifications/services/email.service.spec.ts
```

## Development Tips

1. **Use SES Sandbox**: In development, verify recipient email addresses in SES console
2. **Template Testing**: Create test cases for your templates with sample data
3. **Error Monitoring**: Monitor CloudWatch logs for SES delivery status
4. **Rate Limits**: Be aware of SES sending limits in your AWS account

## Security Considerations

1. **Environment Variables**: Never commit AWS credentials to version control
2. **Email Validation**: Validate email addresses before sending
3. **Rate Limiting**: Implement rate limiting for email sending endpoints
4. **Content Sanitization**: Sanitize user input in template data
