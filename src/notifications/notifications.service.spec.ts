import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotificationsService } from './notifications.service';
import { Notification } from './entities/notification.entity';
import { FirebaseService } from './services/firebase.service';
import { FirebaseTokensService } from 'src/firebase-tokens/firebase-tokens.service';
import { SmsService } from './services/sms.service';
import { EmailService } from './services/email.service';

describe('NotificationsService', () => {
  let service: NotificationsService;
  let emailService: EmailService;
  let repository: Repository<Notification>;

  const mockRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  const mockEmailService = {
    sendEmail: jest.fn(),
  };

  const mockFirebaseService = {
    sendPushNotification: jest.fn(),
  };

  const mockFirebaseTokensService = {
    getTokens: jest.fn(),
  };

  const mockSmsService = {
    sendOtpSms: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationsService,
        {
          provide: getRepositoryToken(Notification),
          useValue: mockRepository,
        },
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
        {
          provide: FirebaseService,
          useValue: mockFirebaseService,
        },
        {
          provide: FirebaseTokensService,
          useValue: mockFirebaseTokensService,
        },
        {
          provide: SmsService,
          useValue: mockSmsService,
        },
      ],
    }).compile();

    service = module.get<NotificationsService>(NotificationsService);
    emailService = module.get<EmailService>(EmailService);
    repository = module.get<Repository<Notification>>(getRepositoryToken(Notification));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendEmailNotification', () => {
    it('should send email notification successfully', async () => {
      mockEmailService.sendEmail.mockResolvedValue(true);

      const recipients = '<EMAIL>';
      const subject = 'Test Subject';
      const templateName = 'forgot-password';
      const templateData = {
        user_name: 'John Doe',
        otp: '123456',
        email: '<EMAIL>',
      };

      const result = await service.sendEmailNotification(
        recipients,
        subject,
        templateName,
        templateData,
      );

      expect(result).toBe(true);
      expect(mockEmailService.sendEmail).toHaveBeenCalledWith({
        to: recipients,
        subject,
        templateName,
        templateData,
      });
    });

    it('should handle multiple recipients', async () => {
      mockEmailService.sendEmail.mockResolvedValue(true);

      const recipients = ['<EMAIL>', '<EMAIL>'];
      const subject = 'Test Subject';
      const templateName = 'welcome';
      const templateData = {
        user_name: 'John Doe',
        app_name: 'Test App',
      };

      const result = await service.sendEmailNotification(
        recipients,
        subject,
        templateName,
        templateData,
      );

      expect(result).toBe(true);
      expect(mockEmailService.sendEmail).toHaveBeenCalledWith({
        to: recipients,
        subject,
        templateName,
        templateData,
      });
    });

    it('should throw error when email service fails', async () => {
      const error = new Error('Email service failed');
      mockEmailService.sendEmail.mockRejectedValue(error);

      const recipients = '<EMAIL>';
      const subject = 'Test Subject';
      const templateName = 'forgot-password';
      const templateData = { user_name: 'John Doe' };

      await expect(
        service.sendEmailNotification(recipients, subject, templateName, templateData),
      ).rejects.toThrow('Email service failed');
    });
  });

  describe('sendOtpSms', () => {
    it('should send OTP SMS successfully', async () => {
      mockSmsService.sendOtpSms.mockResolvedValue(true);

      const phoneNumber = '+1234567890';
      const otpValue = '123456';

      await service.sendOtpSms(phoneNumber, otpValue);

      expect(mockSmsService.sendOtpSms).toHaveBeenCalledWith(phoneNumber, otpValue);
    });
  });
});
