/**
 * Email Service Test Example
 * 
 * This file demonstrates how to test the email functionality.
 * DO NOT run this in production - it's for testing purposes only.
 * 
 * To use this:
 * 1. Make sure your AWS SES credentials are configured in .env
 * 2. Verify your sender email in AWS SES console
 * 3. If in sandbox mode, verify recipient emails too
 * 4. Update the test email addresses below
 * 5. Run: npx ts-node src/notifications/email-test.example.ts
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { NotificationsService } from './notifications.service';

async function testEmailService() {
  console.log('🚀 Starting Email Service Test...\n');

  try {
    // Create NestJS application context
    const app = await NestFactory.createApplicationContext(AppModule);
    const notificationsService = app.get(NotificationsService);

    // Test configuration
    const testConfig = {
      // ⚠️ CHANGE THESE TO YOUR TEST EMAIL ADDRESSES
      senderEmail: '<EMAIL>',  // Must be verified in SES
      recipientEmail: '<EMAIL>',     // Must be verified if in SES sandbox
      
      // Test data
      testUser: {
        name: '<PERSON>',
        email: '<EMAIL>',
        otp: '123456'
      }
    };

    console.log('📧 Test Configuration:');
    console.log(`   Sender: ${testConfig.senderEmail}`);
    console.log(`   Recipient: ${testConfig.recipientEmail}`);
    console.log(`   Test User: ${testConfig.testUser.name}\n`);

    // Test 1: Forgot Password Email
    console.log('🔐 Testing Forgot Password Email...');
    try {
      const currentDate = new Date();
      await notificationsService.sendEmailNotification(
        testConfig.recipientEmail,
        'Password Reset Request - Test',
        'forgot-password',
        {
          user_name: testConfig.testUser.name,
          email: testConfig.testUser.email,
          otp: testConfig.testUser.otp,
          expiry_minutes: '10',
          app_name: 'Zodo Health (Test)',
          current_year: currentDate.getFullYear().toString(),
          sent_date: currentDate.toLocaleDateString(),
          sent_time: currentDate.toLocaleTimeString(),
          app_url: 'https://yourapp.com'
        }
      );
      console.log('   ✅ Forgot password email sent successfully!\n');
    } catch (error) {
      console.log('   ❌ Failed to send forgot password email:');
      console.log(`   Error: ${error.message}\n`);
    }

    // Test 2: Welcome Email
    console.log('👋 Testing Welcome Email...');
    try {
      await notificationsService.sendEmailNotification(
        testConfig.recipientEmail,
        'Welcome to Our Platform - Test',
        'welcome',
        {
          user_name: testConfig.testUser.name,
          app_name: 'Zodo Health (Test)',
          app_url: 'https://yourapp.com',
          current_year: new Date().getFullYear().toString(),
        }
      );
      console.log('   ✅ Welcome email sent successfully!\n');
    } catch (error) {
      console.log('   ❌ Failed to send welcome email:');
      console.log(`   Error: ${error.message}\n`);
    }

    // Test 3: Multiple Recipients
    console.log('👥 Testing Multiple Recipients...');
    try {
      const multipleRecipients = [
        testConfig.recipientEmail,
        // Add more test emails here if needed (must be verified in SES sandbox)
      ];

      await notificationsService.sendEmailNotification(
        multipleRecipients,
        'Multi-Recipient Test Email',
        'welcome',
        {
          user_name: 'Test User',
          app_name: 'Zodo Health (Test)',
          app_url: 'https://yourapp.com',
          current_year: new Date().getFullYear().toString(),
        }
      );
      console.log(`   ✅ Email sent to ${multipleRecipients.length} recipients!\n`);
    } catch (error) {
      console.log('   ❌ Failed to send multi-recipient email:');
      console.log(`   Error: ${error.message}\n`);
    }

    // Test 4: Error Handling (Non-existent template)
    console.log('🚫 Testing Error Handling (Non-existent template)...');
    try {
      await notificationsService.sendEmailNotification(
        testConfig.recipientEmail,
        'Error Test',
        'non-existent-template',
        { test: 'data' }
      );
      console.log('   ❌ This should have failed!\n');
    } catch (error) {
      console.log('   ✅ Error handling works correctly:');
      console.log(`   Expected error: ${error.message}\n`);
    }

    console.log('🎉 Email Service Test Completed!');
    console.log('\n📝 Next Steps:');
    console.log('   1. Check your email inbox for test emails');
    console.log('   2. Verify email formatting and content');
    console.log('   3. Check AWS SES console for delivery status');
    console.log('   4. Monitor CloudWatch logs for any issues');

    await app.close();

  } catch (error) {
    console.error('💥 Test failed with error:', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testEmailService().catch(console.error);
}

export { testEmailService };
