import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Notification } from "./entities/notification.entity";
import {
  CreateNotificationDto,
  NotificationType,
} from "./dto/create-notification.dto";
import { UpdateNotificationDto } from "./dto/update-notification.dto";
import { paginate, PaginateQuery } from "nestjs-paginate";
import { NotificationInterface } from "./interface/notification.interface";
import { FirebaseService } from "./services/firebase.service";
import { FirebaseTokensService } from "src/firebase-tokens/firebase-tokens.service";
import { SmsService } from "./services/sms.service";

@Injectable()
export class NotificationsService {
  constructor(
    @InjectRepository(Notification)
    private readonly notificationRepository: Repository<Notification>,
    private readonly firebaseService: FirebaseService,
    private readonly firebaseTokenService: FirebaseTokensService,
    private readonly smsService: SmsService,
  ) {}

  async send(
    createNotificationDto: CreateNotificationDto,
  ): Promise<Notification> {
    const { type, user_ids, ...data } = createNotificationDto;

    let notification: Notification | null = null;
    user_ids.forEach(async (user_id) => {
      for (const notificationType of type) {
        if (notificationType === NotificationType.IN_APP) {
          await this.sendInAppNotification(user_id, data);
        }

        // if (notificationType === NotificationType.EMAIL) {
        //   // Handle email notification logic
        //   // For example, send an email using an email service
        //   await this.sendEmailNotification(data);
        // }

        if (notificationType === NotificationType.PUSH) {
          // Handle push notification logic
          // For example, send a push notification using a push notification service
          const tokens = await this.firebaseTokenService.getTokens(user_ids);
          await this.firebaseService.sendPushNotification(tokens, data);
        }
      }
    });

    return notification;
  }

  async sendEmailNotification() {
    // Implement email notification logic here
    // For example, use a library like nodemailer to send emails
    // You can access the email data from the 'data' parameter
  }

  private async sendInAppNotification(
    user_id: string,
    data: NotificationInterface,
  ) {
    const notification = this.notificationRepository.create({
      user_id,
      ...data,
    });
    await this.notificationRepository.save(notification);
  }

  async findAll(query: PaginateQuery, user_id: string) {
    // console.log(user_id);
    await this.notificationRepository.update({ user_id }, { is_seen: true });
    return paginate(query, this.notificationRepository, {
      sortableColumns: ["created_at"],
      where: {
        user_id,
      },
    });
  }

  async findOne(id: string): Promise<Notification> {
    const notification = await this.notificationRepository.findOne({
      where: { id },
    });
    if (!notification) {
      throw new NotFoundException(`Notification with ID ${id} not found`);
    }
    return notification;
  }

  async update(
    id: string,
    updateNotificationDto: UpdateNotificationDto,
  ): Promise<Notification> {
    const notification = await this.findOne(id);
    Object.assign(notification, updateNotificationDto);
    return await this.notificationRepository.save(notification);
  }

  async remove(id: string): Promise<void> {
    const notification = await this.findOne(id);
    await this.notificationRepository.remove(notification);
  }

  async sendOtpSms(
    phoneNumber: string,
    otpValue: string,
    templateName?: string,
  ): Promise<any> {
    await this.smsService.sendOtpSms(phoneNumber, otpValue);
  }
}
