import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { NotificationsService } from "./notifications.service";
import { NotificationsController } from "./notifications.controller";
import { Notification } from "./entities/notification.entity";
import { CommonModule } from "src/common/common.module";
import { UsersModule } from "src/users/users.module";
import { FirebaseService } from "./services/firebase.service";
import { FirebaseTokensModule } from "src/firebase-tokens/firebase-tokens.module";
import { SmsService } from "./services/sms.service";

@Module({
  imports: [
    TypeOrmModule.forFeature([Notification]),
    UsersModule,
    CommonModule,
    FirebaseTokensModule,
  ],
  controllers: [NotificationsController],
  providers: [NotificationsService, FirebaseService, SmsService],
  exports: [NotificationsService],
})
export class NotificationsModule {}
