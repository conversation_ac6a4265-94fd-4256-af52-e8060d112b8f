import {
  Is<PERSON>rray,
  IsNotEmpty,
  IsString,
  ArrayNotEmpty,
  IsEnum,
} from "class-validator";

export enum NotificationType {
  EMAIL = "email",
  PUSH = "push",
  IN_APP = "in_app",
  SMS = "sms",
}

export class CreateNotificationDto {
  @IsArray()
  @IsNotEmpty()
  user_ids: string[];

  @IsString()
  @IsNotEmpty()
  title: string;

  @IsString()
  @IsNotEmpty()
  body: string;

  @IsNotEmpty()
  @IsArray()
  @ArrayNotEmpty() // Ensures the array is not empty
  @IsEnum(NotificationType, { each: true }) // Ensures each value in the array is a valid enum value
  type: NotificationType[];
}
