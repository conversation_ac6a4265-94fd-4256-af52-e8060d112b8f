import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Like, Repository } from "typeorm";
import { UserMedicalRecord } from "./entities/user-medical-record.entity";
import { CreateUserMedicalRecordDto } from "./dto/create-user-medical-record.dto";
import { UpdateUserMedicalRecordDto } from "./dto/update-user-medical-record.dto";
import { FileUploadService } from "src/file-upload/file-upload.service";
import { paginate, PaginateQuery } from "nestjs-paginate";

@Injectable()
export class UserMedicalRecordsService {
  constructor(
    @InjectRepository(UserMedicalRecord)
    private readonly userMedicalRecordsRepository: Repository<UserMedicalRecord>,
    private readonly uploadService: FileUploadService,
  ) {}

  async create(dto: CreateUserMedicalRecordDto) {
    const { user_id, child_user_id, files } = dto;

    // Create multiple medical records using the nested DTO structure
    const records = files.map((fileDto) => {
      return this.userMedicalRecordsRepository.create({
        user_id,
        child_user_id: child_user_id,
        file: fileDto.file,
        name: fileDto.name,
        description: fileDto.description,
      });
    });

    // Save all records
    const savedRecords = await this.userMedicalRecordsRepository.save(records);

    return {
      status: true,
      message: "Files added successfully",
      data: savedRecords,
    };
  }

  async findAll(query: PaginateQuery, filters: any) {
    const { user_id, child_user_id, name } = filters;

    // Build the `where` condition dynamically
    let where: any = { user_id };
    if (child_user_id) {
      where.child_user_id = child_user_id;
    }
    if (name) {
      where.name = Like(`%${name}%`);
    }

    // Fetch paginated data
    const data = await paginate(query, this.userMedicalRecordsRepository, {
      sortableColumns: ["created_at"],
      where,
    });

    // Replace file keys with signed URLs
    const updatedRecords = await Promise.all(
      data.data.map(async (record) => {
        const signedUrl = await this.uploadService.getSignedUrl(record.file);
        return {
          ...record,
          file: signedUrl,
        };
      }),
    );

    return {
      ...data,
      data: updatedRecords, // Replace the original data with updated data
    };
  }

  async remove(id: string) {
    const result = await this.userMedicalRecordsRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Medical record with ID ${id} not found`);
    }
    return {
      status: true,
      message: "Medical record deleted successfully",
      data: null,
    };
  }
}
