import {
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsNotEmpty,
  <PERSON><PERSON>te<PERSON>ested,
} from "class-validator";
import { Type } from "class-transformer";

class MedicalRecordFileDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  file: string;

  @IsOptional()
  @IsString()
  description?: string;
}

export class CreateUserMedicalRecordDto {
  @IsNotEmpty()
  @IsString()
  user_id: string;

  @IsOptional()
  @IsString()
  child_user_id?: string;

  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MedicalRecordFileDto)
  files: MedicalRecordFileDto[];
}
