import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UserMedicalRecordsService } from "./user-medical-records.service";
import { UserMedicalRecordsController } from "./user-medical-records.controller";
import { UserMedicalRecord } from "./entities/user-medical-record.entity";
import { FileUploadModule } from "src/file-upload/file-upload.module";

@Module({
  imports: [TypeOrmModule.forFeature([UserMedicalRecord]), FileUploadModule],
  controllers: [UserMedicalRecordsController],
  providers: [UserMedicalRecordsService],
})
export class UserMedicalRecordsModule {}
