import {
  <PERSON>,
  Post,
  Body,
  Get,
  Param,
  Delete,
  Query,
  BadRequestException,
} from "@nestjs/common";
import { UserMedicalRecordsService } from "./user-medical-records.service";
import { CreateUserMedicalRecordDto } from "./dto/create-user-medical-record.dto";
import { Paginate, PaginateQuery } from "nestjs-paginate";

@Controller("user-medical-records")
export class UserMedicalRecordsController {
  constructor(
    private readonly userMedicalRecordsService: UserMedicalRecordsService,
  ) {}

  @Post()
  async create(@Body() dto: CreateUserMedicalRecordDto) {
    return this.userMedicalRecordsService.create(dto);
  }

  @Get()
  async findAll(@Paginate() query: PaginateQuery, @Query() filters: any) {
    if (!filters.user_id) {
      throw new BadRequestException("user_id is required");
    }
    return this.userMedicalRecordsService.findAll(query, filters);
  }

  @Delete(":id")
  async remove(@Param("id") id: string) {
    return this.userMedicalRecordsService.remove(id);
  }
}
