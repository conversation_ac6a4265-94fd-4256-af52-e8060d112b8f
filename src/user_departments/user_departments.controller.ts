import { Controller, Get, Post, Body, Patch, Query } from "@nestjs/common";
import { UserDepartmentsService } from "./user_departments.service";
import { AddUserToDepartmentDto } from "./dto/create-user_department.dto";
import { UpdateUserDepartmentDto } from "./dto/update-user_department.dto";
import { UserDepartments } from "./entities/user_department.entity";

@Controller("user-departments")
export class UserDepartmentsController {
  constructor(
    private readonly userDepartmentsService: UserDepartmentsService,
  ) {}

  @Post()
  async create(@Body() addUserToDepartmentDto: AddUserToDepartmentDto) {
    return this.userDepartmentsService.addUserToDepartment(
      addUserToDepartmentDto,
    );
  }

  @Get("staffs")
  async listStaffsByDepartment(
    @Query("department_id") department_id: string,
  ): Promise<UserDepartments[]> {
    return this.userDepartmentsService.listStaffsByDepartment(department_id);
  }

  @Patch("remove")
  async removeUserFromDepartment(
    @Body() updateUserDepartmentDto: UpdateUserDepartmentDto,
  ): Promise<{ message: string }> {
    const { user_id, department_id } = updateUserDepartmentDto;

    await this.userDepartmentsService.removeUserFromDepartment(
      user_id,
      department_id,
    );

    return {
      message: `User ${user_id} removed from department ${department_id}`,
    };
  }
}
