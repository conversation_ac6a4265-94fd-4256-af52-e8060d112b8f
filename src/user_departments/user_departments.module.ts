import { Module } from "@nestjs/common";
import { UserDepartmentsService } from "./user_departments.service";
import { UserDepartmentsController } from "./user_departments.controller";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UserDepartments } from "./entities/user_department.entity";
import { DepartmentsModule } from "src/departments/departments.module";

@Module({
  imports: [TypeOrmModule.forFeature([UserDepartments]), DepartmentsModule],
  controllers: [UserDepartmentsController],
  providers: [UserDepartmentsService],
  exports: [UserDepartmentsService],
})
export class UserDepartmentsModule {}
