import { Injectable, NotFoundException } from "@nestjs/common";
import { AddUserToDepartmentDto } from "./dto/create-user_department.dto";
import { InjectRepository } from "@nestjs/typeorm";
import { UserDepartments } from "./entities/user_department.entity";
import { Repository } from "typeorm";
import { recordExists } from "src/utils/record-exists";
import { Department } from "src/departments/entities/department.entity";
import { NotFoundError } from "rxjs";

@Injectable()
export class UserDepartmentsService {
  constructor(
    @InjectRepository(UserDepartments)
    private readonly userDepartmentRepository: Repository<UserDepartments>,

    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
  ) {}

  async addUserToDepartment(
    addUserToDepartmentDto: AddUserToDepartmentDto,
  ): Promise<UserDepartments> {
    const userDepartment = this.userDepartmentRepository.create(
      addUserToDepartmentDto,
    );

    return this.userDepartmentRepository.save(userDepartment);
  }

  async removeUserFromDepartment(
    user_id: string,
    department_id: string,
  ): Promise<void> {
    await this.userDepartmentRepository.delete({ user_id, department_id });
  }

  async removeUserFromAllDepartment(user_id: string): Promise<void> {
    await this.userDepartmentRepository.delete({ user_id });
  }

  async deleteUserDepartment(user_id: string): Promise<void> {
    await this.userDepartmentRepository.delete({ user_id });
  }

  async listStaffsByDepartment(
    department_id: string,
  ): Promise<UserDepartments[]> {
    const isExists = await recordExists({
      repository: this.departmentRepository,
      value: department_id,
    });

    if (!isExists) {
      throw new NotFoundException(
        `Department with ${department_id} does not exists`,
      );
    }

    return this.userDepartmentRepository.find({ where: { department_id } });
  }
}
