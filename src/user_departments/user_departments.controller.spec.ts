import { Test, TestingModule } from "@nestjs/testing";
import { UserDepartmentsController } from "./user_departments.controller";
import { UserDepartmentsService } from "./user_departments.service";

describe("UserDepartmentsController", () => {
  let controller: UserDepartmentsController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserDepartmentsController],
      providers: [UserDepartmentsService],
    }).compile();

    controller = module.get<UserDepartmentsController>(
      UserDepartmentsController,
    );
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });
});
