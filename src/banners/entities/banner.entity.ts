import { <PERSON><PERSON><PERSON>, PrimaryColumn, Column, PrimaryGeneratedColumn } from "typeorm";

@Entity("banners")
export class Banner {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", nullable: true })
  title: string;

  @Column({ type: "varchar", nullable: true })
  description: string;

  @Column({ type: "varchar", nullable: true })
  image: string;

  @Column({ type: "varchar", nullable: true })
  url: string;

  @Column({ type: "varchar", length: 36, nullable: true })
  hospital_id: string;
}
