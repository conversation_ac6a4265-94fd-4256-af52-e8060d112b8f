import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UsePipes,
  ValidationPipe,
} from "@nestjs/common";
import { BannersService } from "./banners.service";
import { CreateBannerDto } from "./dto/create-banner.dto";
import { UpdateBannerDto } from "./dto/update-banner.dto";
import { PaginationDto } from "src/common/dto/pagination.dto";
import { Paginate, PaginateQuery } from "nestjs-paginate";

@Controller("banners")
export class BannersController {
  constructor(private readonly bannersService: BannersService) {}

  @Get()
  findAll(@Paginate() query: PaginateQuery) {
    return this.bannersService.findAll(query);
  }

  @Get("user")
  findAllUser(@Paginate() query: PaginateQuery) {
    return this.bannersService.findAllUser(query);
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.bannersService.findOne(id);
  }

  @Post()
  create(@Body() createBannerDto: CreateBannerDto) {
    return this.bannersService.create(createBannerDto);
  }

  @Put(":id")
  update(@Param("id") id: string, @Body() updateBannerDto: UpdateBannerDto) {
    return this.bannersService.update(id, updateBannerDto);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.bannersService.remove(id);
  }
}
