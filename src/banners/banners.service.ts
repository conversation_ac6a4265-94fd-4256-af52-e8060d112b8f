import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import { Banner } from "./entities/banner.entity";
import { CreateBannerDto } from "./dto/create-banner.dto";
import { UpdateBannerDto } from "./dto/update-banner.dto";
import { ResponseService } from "src/common/services/response.service";
import { paginate, PaginateQuery } from "nestjs-paginate";

@Injectable()
export class BannersService {
  constructor(
    @InjectRepository(Banner)
    private readonly bannersRepository: Repository<Banner>,
    private readonly responseService: ResponseService,
  ) {}

  findAll(query: PaginateQuery) {
    return paginate(query, this.bannersRepository, {
      sortableColumns: ["title"],
    });
  }

  findAllUser(query: PaginateQuery) {
    return paginate(query, this.bannersRepository, {
      sortableColumns: ["title"],
    });
  }

  async findOne(id: string) {
    const banner = await this.bannersRepository.findOne({ where: { id } });
    if (!banner) {
      throw new NotFoundException(`Banner with ID ${id} not found`);
    }
    return this.responseService.successResponse(
      "Banner fetched successfully",
      banner,
    );
  }

  async create(createBannerDto: CreateBannerDto) {
    const banner = this.bannersRepository.create(createBannerDto);
    const newBanner = await this.bannersRepository.save(banner);
    return this.responseService.successResponse(
      "Banner created successfully",
      newBanner,
    );
  }

  async update(id: string, updateBannerDto: UpdateBannerDto) {
    await this.bannersRepository.update(id, updateBannerDto);
    return this.responseService.successResponse("Banner updated successfully");
  }

  async remove(id: string) {
    await this.bannersRepository.delete(id);
    return this.responseService.successResponse("Banner removed successfully");
  }
}
