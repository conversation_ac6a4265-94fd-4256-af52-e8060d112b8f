import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Week } from "./entities/week.entity";
import { CreateWeekDto } from "./dto/create-week.dto";
import { UpdateWeekDto } from "./dto/update-week.dto";

@Injectable()
export class WeeksService {
  constructor(
    @InjectRepository(Week)
    private readonly weekRepository: Repository<Week>,
  ) {}

  async create(createWeekDto: CreateWeekDto) {
    return "This action adds a new week";
  }

  async createAllWeekDays(): Promise<Week[]> {
    const weekDays = [
      { name: "Sunday", order: 1 },
      { name: "Monday", order: 2 },
      { name: "Tuesday", order: 3 },
      { name: "Wednesday", order: 4 },
      { name: "Thursday", order: 5 },
      { name: "Friday", order: 6 },
      { name: "Saturday", order: 7 },
    ];

    const createdWeeks: Week[] = [];

    for (const day of weekDays) {
      // Check if a week with the same name already exists
      const existingWeek = await this.weekRepository.findOne({
        where: { name: day.name },
      });

      if (!existingWeek) {
        // Create and save the week if it doesn't exist
        const newWeek = this.weekRepository.create(day);
        const savedWeek = await this.weekRepository.save(newWeek);
        createdWeeks.push(savedWeek);
      }
    }

    return createdWeeks;
  }

  async findAll(): Promise<Week[]> {
    return this.weekRepository.find({
      order: { order: "ASC" }, // Sort by order
    });
  }
  async findWeeksWithAvailabilities(
    doctor_id: string,
    filters: any,
  ): Promise<Week[]> {
    const queryBuilder = this.weekRepository
      .createQueryBuilder("week")
      .leftJoinAndSelect(
        "week.availabilities",
        "availabilities",
        "availabilities.doctor_id = :doctor_id",
        { doctor_id },
      )
      .orderBy("week.order", "ASC"); // Default sorting by order

    // Apply filter for specific week_id
    if (filters.week_id) {
      queryBuilder.andWhere("week.id = :week_id", { week_id: filters.week_id });
    }

    // Apply filter for specific order
    if (filters.order) {
      queryBuilder.andWhere("week.order = :order", { order: filters.order });
    }

    return queryBuilder.getMany();
  }
  async findOne(id: number) {
    return `This action returns a #${id} week`;
  }

  async findOneByOrder(order: number): Promise<Week | null> {
    return await this.weekRepository.findOne({ where: { order } });
  }

  async findOneByWeekName(name: string): Promise<Week | null> {
    return await this.weekRepository.findOne({ where: { name } });
  }

  async update(id: number, updateWeekDto: UpdateWeekDto) {
    return `This action updates a #${id} week`;
  }

  async remove(id: number) {
    return `This action removes a #${id} week`;
  }
}
