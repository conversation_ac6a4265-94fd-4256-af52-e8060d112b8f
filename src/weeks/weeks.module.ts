import { <PERSON>du<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { WeeksService } from "./weeks.service";
import { WeeksController } from "./weeks.controller";
import { Week } from "./entities/week.entity";

@Module({
  imports: [TypeOrmModule.forFeature([Week])],
  controllers: [WeeksController],
  providers: [WeeksService],
  exports: [WeeksService],
})
export class WeeksModule {}
