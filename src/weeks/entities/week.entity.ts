import { Availability } from "src/availabilities/entities/availability.entity";
import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from "typeorm";

@Entity("weeks")
export class Week {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  name: string;

  @Column({ type: "int", nullable: true })
  order: number;

  @OneToMany(() => Availability, (availabilities) => availabilities.week)
  availabilities: Availability[];
}
