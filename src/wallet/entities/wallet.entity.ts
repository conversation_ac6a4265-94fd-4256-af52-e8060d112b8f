import { Hospital } from "src/hospitals/entities/hospital.entity";
import { Doctor } from "src/doctors/entities/doctor.entity";
import {
  En<PERSON><PERSON>,
  PrimaryColumn,
  Column,
  ManyToOne,
  JoinColumn,
  Generated,
} from "typeorm";
@Entity("wallet")
export class Wallet {
  @PrimaryColumn("varchar", { scale: 36 })
  @Generated("uuid")
  id: string;

  @Column({ type: "varchar", length: 36, nullable: true })
  hospital_id: string | null;

  @Column({ type: "varchar", length: 36, nullable: true })
  doctor_id: string | null;

  @Column({
    type: "decimal",
    precision: 10,
    scale: 2,
    default: 0,
    nullable: true,
  })
  total_amount: string;

  @Column({
    type: "decimal",
    precision: 10,
    scale: 2,
    default: 0,
    nullable: true,
  })
  requested_amount: string;

  @Column({
    type: "decimal",
    precision: 10,
    scale: 2,
    default: 0,
    nullable: true,
  })
  balance_amount: string;

  @Column({
    type: "decimal",
    precision: 10,
    scale: 2,
    default: 0,
    nullable: true,
  })
  withdrawal_amount: string;

  @ManyToOne(() => Doctor, (doctor) => doctor.wallets, { onDelete: "CASCADE" })
  @JoinColumn({ name: "doctor_id" })
  doctor: Doctor;

  @ManyToOne(() => Hospital, (hospital) => hospital.wallets, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "hospital_id" })
  hospital: Hospital;
}
