import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Wallet } from "./entities/wallet.entity";
import { Transaction } from "src/transactions/entities/transaction.entity";
import { CreateWalletDto } from "./dto/create-wallet.dto";
import { UpdateWalletDto } from "./dto/update-wallet.dto";
import { TransactionStatus } from "src/common/enums/status.enum";
import { TransactionsService } from "src/transactions/transactions.service";
import { SettlementStatus } from "src/settlements/entities/settlement.entity";
import { SettlementsService } from "src/settlements/settlements.service";

export interface WalletUpdateResult {
  status: boolean;
  message: string;
  data: Wallet;
  added_amount: number;
  latest_settlement?: any;
}

@Injectable()
export class WalletService {
  constructor(
    @InjectRepository(Wallet)
    private readonly walletRepository: Repository<Wallet>,
    // @InjectRepository(Transaction)
    // private readonly transactionRepository: Repository<Transaction>,
    private readonly transactionsService: TransactionsService,
    @Inject(forwardRef(() => SettlementsService))
    private readonly settlementsService: SettlementsService,
  ) {}

  create(createWalletDto: CreateWalletDto) {
    return "This action adds a new wallet";
  }

  findAll() {
    return `This action returns all wallet`;
  }

  findOne(id: number) {
    return `This action returns a #${id} wallet`;
  }

  update(id: number, updateWalletDto: UpdateWalletDto) {
    return `This action updates a #${id} wallet`;
  }

  remove(id: number) {
    return `This action removes a #${id} wallet`;
  }

  async fetchAndUpdateHospitalWallet(
    hospital_id: string,
  ): Promise<WalletUpdateResult> {
    // 1. Calculate total amount from transactions
    const transactions =
      await this.transactionsService.findTransactionsForWalletUpdateHospital(
        hospital_id,
      );

    const total_amount = transactions.reduce(
      (sum, t) => sum + Number(t.amount),
      0,
    );

    // 3. Fetch or create the wallet
    let wallet = await this.walletRepository.findOne({
      where: { hospital_id },
    });
    if (!wallet) {
      wallet = this.walletRepository.create({
        hospital_id,
        total_amount: "0",
        balance_amount: "0",
        requested_amount: "0",
        withdrawal_amount: "0",
      });
    }

    // Safely convert string to number, add, then convert back to string
    wallet.balance_amount = (
      Number(wallet.balance_amount) + total_amount
    ).toString();
    wallet.total_amount = (
      Number(wallet.total_amount) + total_amount
    ).toString();

    await this.walletRepository.save(wallet);

    // 2. Update wallet_updated to true for these transactions
    if (transactions.length > 0) {
      const ids = transactions.map((t) => t.id);
      await this.transactionsService.updateTransactionsAsWalletUpdated(ids);
    }
    return {
      status: true,
      message: "Wallet details fetched and updated successfully",
      data: wallet,
      added_amount: total_amount,
      latest_settlement:
        await this.settlementsService.getLatestSettlementByHospital(
          hospital_id,
        ),
    };
  }

  async fetchAndUpdateHospitalWalletDoctor(
    doctor_id: string,
  ): Promise<WalletUpdateResult> {
    const transactions =
      await this.transactionsService.findTransactionsForWalletUpdateDoctor(
        doctor_id,
      );

    const total_amount = transactions.reduce(
      (sum, t) => sum + Number(t.amount),
      0,
    );

    // 3. Fetch or create the wallet
    let wallet = await this.walletRepository.findOne({ where: { doctor_id } });
    if (!wallet) {
      wallet = this.walletRepository.create({
        doctor_id,
        total_amount: "0",
        balance_amount: "0",
        requested_amount: "0",
        withdrawal_amount: "0",
      });
    }

    // Safely convert string to number, add, then convert back to string
    wallet.balance_amount = (
      Number(wallet.balance_amount) + total_amount
    ).toString();
    wallet.total_amount = (
      Number(wallet.total_amount) + total_amount
    ).toString();

    await this.walletRepository.save(wallet);

    // 2. Update wallet_updated to true for these transactions
    if (transactions.length > 0) {
      const ids = transactions.map((t) => t.id);
      await this.transactionsService.updateTransactionsAsWalletUpdated(ids);
    }
    return {
      status: true,
      message: "Wallet details fetched and updated successfully",
      data: wallet,
      added_amount: total_amount,
    };
  }

  async updateBalanceAmount(wallet: Wallet, amount: number) {
    // const wallet = await this.walletRepository.findOneBy({ id: wallet_id })
    return await this.walletRepository.update(
      { id: wallet.id },
      {
        balance_amount: (Number(wallet.balance_amount) - amount).toString(),
        requested_amount: (
          Number(wallet.requested_amount) + Number(amount)
        ).toString(),
      },
    );
  }

  async updateWithdrawalAmount(
    wallet: Wallet,
    amount: number,
    status: SettlementStatus,
  ) {
    if (status == SettlementStatus.APPROVED) {
      return await this.walletRepository.update(
        { id: wallet.id },
        {
          withdrawal_amount: (
            Number(wallet.withdrawal_amount) + Number(amount)
          ).toString(),
          requested_amount: (
            Number(wallet.requested_amount) - amount
          ).toString(),
        },
      );
    } else if (status == SettlementStatus.REJECTED) {
      return await this.walletRepository.update(
        { id: wallet.id },
        {
          balance_amount: (
            Number(wallet.balance_amount) + Number(amount)
          ).toString(),
          requested_amount: (
            Number(wallet.requested_amount) - amount
          ).toString(),
        },
      );
    }
    // const wallet = await this.walletRepository.findOneBy({ id: wallet_id })
  }
}
