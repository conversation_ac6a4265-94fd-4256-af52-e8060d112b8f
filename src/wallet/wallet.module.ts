import { forwardRef, <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { WalletService } from "./wallet.service";
import { WalletController } from "./wallet.controller";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Wallet } from "./entities/wallet.entity";
import { Transaction } from "typeorm";
import { TransactionsModule } from "src/transactions/transactions.module";
import { SettlementsModule } from "src/settlements/settlements.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([Wallet]),
    forwardRef(() => SettlementsModule),
    TransactionsModule,
  ],
  controllers: [WalletController],
  providers: [WalletService],
  exports: [WalletService],
})
export class WalletModule {}
