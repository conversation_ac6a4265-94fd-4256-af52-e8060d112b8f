import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from "@nestjs/common";
import { WalletService } from "./wallet.service";
import { CreateWalletDto } from "./dto/create-wallet.dto";
import { UpdateWalletDto } from "./dto/update-wallet.dto";

@Controller("wallet")
export class WalletController {
  constructor(private readonly walletService: WalletService) {}

  @Post()
  create(@Body() createWalletDto: CreateWalletDto) {
    return this.walletService.create(createWalletDto);
  }

  @Get()
  findAll() {
    return this.walletService.findAll();
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.walletService.findOne(+id);
  }

  @Patch(":id")
  update(@Param("id") id: string, @Body() updateWalletDto: UpdateWalletDto) {
    return this.walletService.update(+id, updateWalletDto);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.walletService.remove(+id);
  }

  @Get("hospital/:hospital_id")
  async fetchAndUpdateHospitalWallet(
    @Param("hospital_id") hospital_id: string,
  ) {
    return this.walletService.fetchAndUpdateHospitalWallet(hospital_id);
  }

  @Get("doctors/:doctor_id")
  async fetchAndUpdateHospitalWalletDoctor(
    @Param("doctor_id") doctor_id: string,
  ) {
    return this.walletService.fetchAndUpdateHospitalWalletDoctor(doctor_id);
  }
}
