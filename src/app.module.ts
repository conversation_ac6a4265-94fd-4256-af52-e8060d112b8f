import { Module } from "@nestjs/common";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { ConfigModule } from "@nestjs/config";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UsersModule } from "./users/users.module";
import { RolesModule } from "./roles/roles.module";
import { AuthModule } from "./auth/auth.module";
import { CommonModule } from "./common/common.module";
import { HospitalsModule } from "./hospitals/hospitals.module";
import { DoctorsModule } from "./doctors/doctors.module";
import { SpecialisationsModule } from "./specialisations/specialisations.module";
import { DepartmentsModule } from "./departments/departments.module";
import { UserDepartmentsModule } from "./user_departments/user_departments.module";
import { DoctorSpecialisationModule } from "./doctor_specialisation/doctor_specialisation.module";
import { DistrictsModule } from "./districts/districts.module";
import { ChildUsersModule } from "./child_users/child_users.module";
import { TimeSlotModule } from "./time-slots/time-slots.module";
import { HospitalServicesModule } from "./hospital-services/hospital-services.module";
import { BannersModule } from "./banners/banners.module";
import { SettlementsModule } from "./settlements/settlements.module";
import { CashfreeModule } from "./cashfree/cashfree.module";
import { TransactionsModule } from "./transactions/transactions.module";
import { FileUploadModule } from "./file-upload/file-upload.module";
import { NotificationsModule } from "./notifications/notifications.module";
import { CouponsModule } from "./coupons/coupons.module";
import { BookingPrescriptionsModule } from "./booking-prescriptions/booking-prescriptions.module";
import { UserMedicalRecordsModule } from "./user-medical-records/user-medical-records.module";
import { AppDetailsModule } from "./app-details/app-details.module";
import { DocumentsModule } from "./documents/documents.module";
import { DashboardModule } from "./dashboard/dashboard.module";
import { WeeksModule } from "./weeks/weeks.module";
import { AvailabilitiesModule } from "./availabilities/availabilities.module";
import { FirebaseTokensModule } from "./firebase-tokens/firebase-tokens.module";
import { WalletModule } from "./wallet/wallet.module";
import { BookingModule } from "./bookings/bookings.module";

@Module({
  imports: [
    ConfigModule.forRoot(),
    TypeOrmModule.forRootAsync({
      useFactory: () => ({
        type: "mysql",
        host: process.env.DB_HOST,
        port: parseInt(process.env.DB_PORT) || 3306,
        username: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        autoLoadEntities: true,
      }),
    }),
    UsersModule,
    RolesModule,
    AuthModule,
    CommonModule,
    HospitalsModule,
    DoctorsModule,
    SpecialisationsModule,
    DepartmentsModule,
    UserDepartmentsModule,
    DoctorSpecialisationModule,
    DistrictsModule,
    ChildUsersModule,
    BookingModule,
    TimeSlotModule,
    HospitalServicesModule,
    BannersModule,
    SettlementsModule,
    CashfreeModule,
    TransactionsModule,
    FileUploadModule,
    NotificationsModule,
    CouponsModule,
    BookingPrescriptionsModule,
    UserMedicalRecordsModule,
    AppDetailsModule,
    DocumentsModule,
    DashboardModule,
    WeeksModule,
    AvailabilitiesModule,
    FirebaseTokensModule,
    WalletModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
