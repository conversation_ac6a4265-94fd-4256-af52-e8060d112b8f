import { Module } from "@nestjs/common";
import { ResponseService } from "./services/response.service";
import { ConfigModule } from "@nestjs/config";
import { UsersModule } from "src/users/users.module";
import { OtpService } from "./services/otp-service.service";

@Module({
  controllers: [],
  imports: [ConfigModule, UsersModule],
  providers: [ResponseService, OtpService],
  exports: [
    CommonModule,
    ConfigModule,
    ResponseService,
    UsersModule,
    OtpService,
  ],
})
export class CommonModule {}
