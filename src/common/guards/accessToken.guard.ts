import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from "@nestjs/common";
import * as jwt from "jsonwebtoken";
import { Request } from "express";
import { JwtService } from "@nestjs/jwt";
import { UsersService } from "src/users/users.service";
import { error } from "console";

import { access } from "fs";
import {
  JWT_ACCESS_SECRET,
  JWT_REFRESH_SECRET,
} from "../constants/jwt.constants";

@Injectable()
export class AccessTokenGuard implements CanActivate {
  constructor(private userService: UsersService) {}
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = this.getRequest(context);
    const token = this.extractToken(request);
    let tokens = null;
    if (!token) {
      this.throwUnauthorized("No token provided");
    }

    try {
      const decodedToken = this.verifyToken(token);
      const uid = decodedToken["sub"];
      request["uid"] = uid;
      // request["user"] = await this.checkWheterUserExists(uid);
      return true;
    } catch (error) {
      this.throwUnauthorized("Invalid or expired token");
    }
  }

  private getRequest(context: ExecutionContext): Request {
    return context.switchToHttp().getRequest();
  }

  private extractToken(request: Request): string | undefined {
    const authorization = request.headers.authorization;
    if (!authorization) return undefined;

    const [type, token] = authorization.split(" ");
    return type === "Bearer" ? token : undefined;
  }

  private verifyToken(token: string): any {
    return jwt.verify(token, JWT_ACCESS_SECRET);
  }

  private verifyRefreshToken(token: string): any {
    return jwt.verify(token, JWT_REFRESH_SECRET);
  }

  private throwUnauthorized(message: string, access_token?: string): never {
    throw new UnauthorizedException({
      status: false,
      message,
      token: access_token,
    });
  }

  private decodeToken(token) {
    return jwt.decode(token) as {
      refreshToken: string;
      u: string;
      sub: string;
    };
  }

  async checkWheterUserExists(uid: string) {
    const user = await this.userService.findOneByParam({ id: uid });
    if (!user) {
      this.throwUnauthorized("Invalid user token");
    }
    return user;
  }
}
