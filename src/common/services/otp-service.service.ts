import { Injectable } from "@nestjs/common";
import * as OTP<PERSON><PERSON> from "otpauth";
const crypto = require("crypto");

@Injectable()
export class OtpService {
  generateOtp(secret: string) {
    let totp = this.iniatlizeOTPAuth(secret);
    return totp.generate();
  }

  validateOtp(token, secret) {
    let totp = this.iniatlizeOTPAuth(secret);

    return totp.validate({ token, window: 1 });
  }

  iniatlizeOTPAuth(secret: string) {
    return new OTPAuth.TOTP({
      algorithm: "SHA1",
      digits: 4,
      period: 300,
      secret,
    });
  }

  generateSecret() {
    // const str = OTPAuth.Secret.fromHex(string).hex;
    const str = crypto
      .createHash("sha256")
      .update(new Date().toISOString())
      .digest("hex");
    return str.replace(/[^a-zA-Z]/g, "");
  }
}
