import { Modu<PERSON> } from "@nestjs/common";
import { DoctorsService } from "./doctors.service";
import { DoctorsController } from "./doctors.controller";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Doctor } from "./entities/doctor.entity";
import { UsersModule } from "src/users/users.module";
import { DistrictsModule } from "src/districts/districts.module";
import { CommonModule } from "src/common/common.module";
import { SpecialisationsModule } from "src/specialisations/specialisations.module";
import { UserDepartmentsModule } from "src/user_departments/user_departments.module";
import { ReviewsModule } from "src/reviews/reviews.module";
import { Review } from "src/reviews/entities/review.entity";
import { DocumentsModule } from "src/documents/documents.module";
import { AvailabilitiesModule } from "src/availabilities/availabilities.module";
import { HospitalsModule } from "src/hospitals/hospitals.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([Doctor, Review]),
    UsersModule,
    SpecialisationsModule,
    DistrictsModule,
    CommonModule,
    UserDepartmentsModule,
    ReviewsModule,
    DocumentsModule,
    AvailabilitiesModule,
    HospitalsModule,
  ],
  controllers: [DoctorsController],
  providers: [DoctorsService],
  exports: [TypeOrmModule, DoctorsService],
})
export class DoctorsModule {}
