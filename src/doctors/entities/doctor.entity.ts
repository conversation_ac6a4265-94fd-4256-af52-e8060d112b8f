import { Department } from "src/departments/entities/department.entity";
import { Hospital } from "src/hospitals/entities/hospital.entity";
import { Specialisations } from "src/specialisations/entities/specialisation.entity";
import { <PERSON><PERSON><PERSON><PERSON>, DoctorAddress } from "src/types/doctors.type";
import { User } from "src/users/entities/user.entity";
import { Review } from "src/reviews/entities/review.entity";
import {
  Column,
  Entity,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import { Transaction } from "src/transactions/entities/transaction.entity";
import { Wallet } from "src/wallet/entities/wallet.entity";

export enum DoctorType {
  ONLINE = "online",
  OFFLINE = "offline",
}

@Entity({ name: "doctors" })
export class Doctor {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ nullable: true })
  profile_pic: string;

  @Column({ type: "varchar", length: 255 })
  name: string;

  @Column({ type: "text", nullable: true }) // Add the about column
  about: string | null;

  @Column({ nullable: false })
  email: string;

  @Column({ nullable: true })
  city: string;

  @Column({ nullable: true })
  slug: string;

  @Column("enum", {
    enum: ["pending", "active", "disabled", "rejected"],
    nullable: true,
  })
  status: string;

  @Column("simple-json", { nullable: true })
  registration_details: Record<string, string>;

  @Column("simple-json", { nullable: true })
  address: DoctorAddress;

  @Column("simple-json", { nullable: true })
  bank_details: BankDetails;

  // @Column("simple-json", { nullable: true })
  // documents: Record<string, string>;

  @Column({ nullable: true })
  hospital_id?: string;

  @Column({ nullable: false })
  user_id: string;

  @Column({ nullable: false })
  phone_number: string;

  @Column({ type: "decimal", precision: 10, scale: 2, nullable: true })
  pricing: number;

  @ManyToOne(() => Hospital, (hospital) => hospital.doctors, { nullable: true })
  @JoinColumn({ name: "hospital_id" }) // Ensure the column is properly named
  hospital?: Hospital;

  @ManyToMany(() => Specialisations)
  @JoinTable({
    name: "doctor_specialisation",
    joinColumn: {
      name: "doctor_id",
    },
    inverseJoinColumn: {
      name: "specialisation_id",
    },
  })
  specialisations: Specialisations[];

  @OneToOne(() => User, { nullable: false }) // Define the one-to-one relationship
  @JoinColumn({ name: "user_id" }) // Specify the join column
  user: User;

  @Column({ nullable: true })
  work_start_date?: Date;

  @ManyToMany(() => Department)
  @JoinTable({
    name: "user_departments",
    joinColumn: {
      name: "doctor_id",
    },
    inverseJoinColumn: {
      name: "department_id",
    },
  })
  departments: Department[];

  @OneToMany(() => Review, (review) => review.doctor)
  reviews: Review[];

  @Column({ type: "decimal", precision: 10, scale: 2, default: 0 })
  total_rating: number;

  @Column({ type: "decimal", precision: 3, scale: 2, default: 0 })
  avg_rating: number;

  @Column({ type: "int", default: 0 })
  rating_count: number;

  @Column({ type: "boolean", default: true })
  auto_booking_enabled: boolean;

  @Column({ type: "int", nullable: true, default: 15 }) // Add consultation_duration column
  consultation_duration: number | null;

  @OneToMany(() => Transaction, (review) => review.doctor)
  transactions: Transaction[];

  @OneToMany(() => Wallet, (wallet) => wallet.doctor)
  wallets: Wallet[];

  @Column({ type: "boolean", default: false })
  from_web: boolean;
}
