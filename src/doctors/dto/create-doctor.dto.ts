import { Type } from "class-transformer";
import {
  IsArray,
  IsEmail,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  IsNotEmpty,
  ValidateNested,
  IsInt,
  Min,
  IsDateString,
  IsBoolean,
} from "class-validator";
import { FileDto } from "src/documents/dto/create-multiple-files.dto";
import { <PERSON><PERSON><PERSON><PERSON>, DoctorAddress } from "src/types/doctors.type";

export class CreateDoctorDto {
  @IsUUID()
  @IsOptional()
  id?: string;

  @IsUUID()
  @IsOptional()
  user_id?: string;

  @IsUUID()
  @IsOptional()
  district_id?: string;

  @IsString()
  @IsOptional()
  profile_pic?: string;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  hospital_id: string;

  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  department_id?: string[];

  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  specifications_id?: string[];

  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  department_ids?: string[];

  @IsString()
  @IsOptional()
  city?: string;

  @IsObject()
  @IsOptional()
  registration_details?: Record<string, string>;

  @IsObject()
  @IsOptional()
  address?: DoctorAddress;

  @IsObject()
  @IsOptional()
  bank_details?: BankDetails;

  @IsArray()
  @ValidateNested({ each: true }) // Validate each object in the array
  @Type(() => FileDto) // Transform each item in the array to an instance of FileDto
  documents?: FileDto[];

  @IsOptional()
  @IsString()
  phone_number?: string;

  @IsOptional()
  @IsNumber()
  pricing?: number;

  @IsOptional()
  @IsString()
  password?: string;

  @IsOptional()
  @IsString()
  about?: string; // Add the about field

  @IsOptional()
  @IsString()
  status?: string; // Add the about field

  @IsOptional()
  @IsInt()
  @Min(1) // Ensure the duration is at least 1 minute
  consultation_duration?: number;

  @IsOptional()
  @IsDateString()
  work_start_date?: string;

  @IsBoolean()
  @IsOptional()
  from_web: boolean;
}
