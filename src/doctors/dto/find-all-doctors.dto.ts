import { IsEnum, IsOptional, IsString } from "class-validator";
import { DoctorType } from "../entities/doctor.entity";
import { PaginationDto } from "src/common/dto/pagination.dto";

export class FindAllDoctorsDto extends PaginationDto {
  @IsOptional()
  @IsEnum(DoctorType)
  doctor_type?: DoctorType;

  @IsOptional()
  @IsString()
  district_id?: string;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  hospital_id?: string;

  @IsOptional()
  @IsString()
  gender?: string;

  @IsOptional()
  @IsString()
  specialisation_ids?: string;

  @IsOptional()
  price_from?: number;

  @IsOptional()
  price_to?: number;

  @IsOptional()
  status?: string;
}
