import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { CreateDoctorDto } from "./dto/create-doctor.dto";
import { UpdateDoctorDto } from "./dto/update-doctor.dto";
import { InjectRepository } from "@nestjs/typeorm";
import { Doctor, DoctorType } from "./entities/doctor.entity";
import { Between, In, IsNull, Like, Not, Repository } from "typeorm";
import { UsersService } from "src/users/users.service";
import { CreateUserDto } from "src/users/dto/create-user.dto";
import { paginate, Paginated, PaginateQuery } from "nestjs-paginate";
import { FindAllDoctorsDto } from "./dto/find-all-doctors.dto";
import { DistrictsService } from "src/districts/districts.service";
import { ResponseService } from "src/common/services/response.service";
import { Gender } from "src/users/entities/user.entity";
import { SpecialisationsService } from "src/specialisations/specialisations.service";
import { UserDepartmentsService } from "src/user_departments/user_departments.service";
import { ChangeDoctorStatusDto } from "./dto/change-doctor-status.dto";
import { Review } from "src/reviews/entities/review.entity";
import { DocumentsService } from "src/documents/documents.service";
import { AvailabilitiesService } from "src/availabilities/availabilities.service";
import { HospitalsService } from "src/hospitals/hospitals.service";

@Injectable()
export class DoctorsService {
  constructor(
    @InjectRepository(Doctor)
    private doctorRepository: Repository<Doctor>,
    @InjectRepository(Review)
    private readonly reviewRepository: Repository<Review>,
    private readonly userService: UsersService,
    private readonly districtService: DistrictsService,
    private readonly responseService: ResponseService,
    private readonly specialisationService: SpecialisationsService,
    private readonly userDepartmentsService: UserDepartmentsService,
    private readonly documentsService: DocumentsService,
    private readonly availabilityService: AvailabilitiesService,
    @Inject(forwardRef(() => HospitalsService))
    private readonly hospitalService: HospitalsService,
  ) {}

  async create(createDoctorDto: CreateDoctorDto) {
    const userExists = await this.userService.findOneByParam({
      email: createDoctorDto.email,
    });

    if (userExists) {
      throw new BadRequestException("User already exists wit this email");
    }
    let district_id = createDoctorDto.district_id ?? null;
    if (createDoctorDto.hospital_id) {
      const hospital = await this.hospitalService.findById(
        createDoctorDto.hospital_id,
      );
      district_id = hospital.district_id;
    }
    const docUser: CreateUserDto = {
      first_name: createDoctorDto.name,
      role: "doctor",
      email: createDoctorDto.email,
      phone: createDoctorDto.phone_number,
      is_active: createDoctorDto.status == "active" ? true : false, // Set is_active based on status
      password: createDoctorDto.password || "Password",
      user_type: "doctor",
      profile_picture: createDoctorDto.profile_pic || null,
      district_id: district_id || null,
      hospital_id: createDoctorDto.hospital_id ?? null,
    };
    const user = await this.userService.create(docUser);
    const doctorData = this.doctorRepository.create({
      user_id: user.id,
      name: createDoctorDto.name,
      email: createDoctorDto.email,
      phone_number: createDoctorDto.phone_number,
      profile_pic: createDoctorDto.profile_pic || null,
      city: createDoctorDto.city || null,
      registration_details: createDoctorDto.registration_details || null,
      address: createDoctorDto.address || null,
      bank_details: createDoctorDto.bank_details || null,
      hospital_id: createDoctorDto.hospital_id || null,
      pricing: createDoctorDto.pricing,
      about: createDoctorDto.about || null,
      status: createDoctorDto.status
        ? createDoctorDto.status
        : createDoctorDto.hospital_id
          ? "active"
          : "pending",
      consultation_duration: createDoctorDto.consultation_duration ?? 15,
      work_start_date: createDoctorDto.work_start_date ?? null,
      slug: await this.generateUniqueSlug(createDoctorDto.name),
    });

    const doctor = await this.doctorRepository.save(doctorData);

    if (createDoctorDto.specifications_id) {
      const specialisations = await this.specialisationService.findByIds(
        createDoctorDto.specifications_id,
      );
      doctor.specialisations = specialisations;
      await this.doctorRepository.save(doctor);
    }
    if (createDoctorDto.department_ids && createDoctorDto.hospital_id) {
      createDoctorDto.department_ids.forEach(async (element) => {
        await this.userDepartmentsService.addUserToDepartment({
          user_id: user.id,
          department_id: element,
          doctor_id: doctor.id,
        });
      });
    }
    if (createDoctorDto.documents) {
      await this.documentsService.createMultipleFiles({
        doctor_id: doctor.id,
        documents: createDoctorDto.documents,
      });
    }
    await this.availabilityService.addAvailability(doctor.id);
    return doctor;
  }

  async findAll(
    query: PaginateQuery,
    filters: FindAllDoctorsDto,
  ): Promise<Paginated<Doctor>> {
    let where: any = {
      id: Not(IsNull()),
    };

    if (filters.name) {
      where.name = Like(`%${filters.name}%`);
    }

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.hospital_id) {
      where.hospital_id = filters.hospital_id;
    } else {
      where.hospital_id = IsNull();
    }
    if (filters.doctor_type) {
      where.hospital_id =
        filters.doctor_type == "online" ? IsNull() : Not(IsNull());
    }

    if (filters.gender || filters.district_id) {
      where.user = {};
      if (filters.district_id) {
        where.user.district_id = filters.district_id;
      }
      if (filters.gender) {
        where.user.Gender = filters.gender;
      }
    }

    if (filters.specialisation_ids) {
      const specialisationIds = filters.specialisation_ids.split(",");
      where.specialisations = { id: In(specialisationIds) };
    }

    if (filters.price_from && filters.price_to) {
      where.pricing = Between(filters.price_from, filters.price_to);
    }
    return paginate(query, this.doctorRepository, {
      sortableColumns: ["name"],
      relations: ["hospital", "specialisations", "departments"],
      where: where,
    });
  }

  async findOne(id: string): Promise<Doctor> {
    const doctor = await this.doctorRepository.findOne({
      where: { id },
      relations: ["hospital", "specialisations", "departments"],
    });
    if (!doctor) {
      throw new NotFoundException(`Doctor with ID ${id} not found`);
    }
    return doctor;
  }

  async findOneUser(id: string) {
    const doctor = await this.doctorRepository.findOne({
      where: { id },
      relations: ["hospital", "specialisations"],
    });
    if (!doctor) {
      throw new NotFoundException(`Doctor with ID ${id} not found`);
    }
    return this.responseService.successResponse(
      "Doctor fetched successfully",
      doctor,
    );
  }
  async findBySlug(slug: string): Promise<Doctor> {
    const doctor = await this.doctorRepository.findOne({ where: { slug } });
    if (!doctor) {
      throw new NotFoundException(`doctor with slug '${slug}' not found.`);
    }
    return doctor;
  }

  async update(id: string, updateDoctorDto: UpdateDoctorDto): Promise<Doctor> {
    const { specifications_id, department_ids, documents, ...data } =
      updateDoctorDto;
    await this.doctorRepository.update(id, data);
    const updatedDoctor = await this.doctorRepository.findOne({
      where: { id },
    });
    if (!updatedDoctor) {
      throw new NotFoundException(`Doctor with ID ${id} not found`);
    }

    const user = await this.userService.findOneByParam({
      id: updatedDoctor.user_id,
    });
    if (user) {
      const updatedUser = {
        ...user,
        first_name: updateDoctorDto.name ?? user.first_name,
        email: updateDoctorDto.email ?? user.email,
        phone: updateDoctorDto.phone_number ?? user.phone,
      };
      await this.userService.update(user.id, updatedUser);
    }
    if (updateDoctorDto.specifications_id) {
      const specialisations = await this.specialisationService.findByIds(
        updateDoctorDto.specifications_id,
      );
      updatedDoctor.specialisations = specialisations;
      await this.doctorRepository.save(updatedDoctor);
    }
    await this.userDepartmentsService.removeUserFromAllDepartment(
      updatedDoctor.user_id,
    );
    if (updateDoctorDto.department_ids && updatedDoctor.hospital_id) {
      updateDoctorDto.department_ids.forEach(async (element) => {
        await this.userDepartmentsService.addUserToDepartment({
          user_id: updatedDoctor.user_id,
          department_id: element,
          doctor_id: updatedDoctor.id,
        });
      });
    }
    if (documents) {
      await this.documentsService.createMultipleFiles({
        doctor_id: updatedDoctor.id,
        documents: documents,
      });
    }
    return await this.doctorRepository.findOne({ where: { id } });
  }

  async remove(id: string): Promise<void> {
    const result = await this.doctorRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Doctor with ID ${id} not found`);
    }
  }

  async findById(id: string): Promise<Doctor> {
    return await this.doctorRepository.findOne({ where: { id } });
  }

  async changeStatus(
    id: string,
    changeDoctorStatusDto: ChangeDoctorStatusDto,
  ): Promise<any> {
    const doctor = await this.doctorRepository.findOne({ where: { id } });

    if (!doctor) {
      throw new NotFoundException(`Doctor with ID ${id} not found`);
    }

    doctor.status = changeDoctorStatusDto.status;
    await this.doctorRepository.save(doctor);
    if (changeDoctorStatusDto.status === "active") {
      // If the doctor is being activated, ensure the user is also activated
      const user = await this.userService.findOneByParam({
        id: doctor.user_id,
      });
      if (user) {
        user.is_active = true; // Activate the user
        await this.userService.update(user.id, user);
      }
    } else if (
      changeDoctorStatusDto.status === "disabled" ||
      changeDoctorStatusDto.status === "rejected"
    ) {
      // If the doctor is being deactivated, ensure the user is also deactivated
      const user = await this.userService.findOneByParam({
        id: doctor.user_id,
      });
      if (user) {
        user.is_active = false; // Deactivate the user
        await this.userService.update(user.id, user);
      }
    }
    return {
      status: true,
      message: "Doctor status updated successfully",
    };
  }

  async calculateRatings(doctor_id: string): Promise<void> {
    const reviews = await this.reviewRepository.find({
      where: { doctor_id },
      select: ["rating"],
    });
    if (!reviews.length) {
      await this.doctorRepository.update(doctor_id, {
        total_rating: 0,
        avg_rating: 0,
        rating_count: 0,
      });
      return;
    }

    const totalRating = reviews.reduce(
      (sum, review) => sum + Number(review.rating),
      0,
    );
    const ratingCount = reviews.length;
    const avgRating = totalRating / ratingCount;

    await this.doctorRepository.update(doctor_id, {
      total_rating: totalRating,
      avg_rating: parseFloat(avgRating.toFixed(2)),
      rating_count: ratingCount,
    });
  }

  async findOneByParam(params: {
    [key: string]: any;
  }): Promise<Doctor | undefined> {
    return await this.doctorRepository.findOneBy(params);
  }

  async me(user_id: string) {
    const doctor = await this.doctorRepository.findOneBy({
      user_id,
    });
    return {
      status: true,
      message: "doctor",
      data: doctor,
    };
  }

  async toggleAutoBooking(id: string, enabled: boolean): Promise<Doctor> {
    const doctor = await this.findOne(id);
    doctor.auto_booking_enabled = enabled;
    return await this.doctorRepository.save(doctor);
  }

  async updateDistrictId(hospital_id: string, district_id: string) {
    const doctors = await this.doctorRepository.find({
      where: { hospital_id },
      select: ["id", "user_id"],
    });

    // Update district_id for each doctor's user
    for (const doctor of doctors) {
      await this.userService.update(doctor.user_id, {
        district_id: district_id,
      });
    }
  }

  async generateUniqueSlug(name: string): Promise<string> {
    // Create base slug by converting to lowercase and replacing spaces with underscores
    const baseSlug = name
      .toLowerCase()
      .replace(/\s+/g, "_")
      .replace(/[^a-z0-9_]/g, "");
    // Check if the base slug exists
    const existingDoctor = await this.doctorRepository.findOne({
      where: { slug: baseSlug },
    });

    if (!existingDoctor) {
      return baseSlug;
    }

    // If base slug exists, find the next available number
    let counter = 1;
    let uniqueSlug = `${baseSlug}-${counter}`;

    while (true) {
      const existingWithCounter = await this.doctorRepository.findOne({
        where: { slug: uniqueSlug },
      });

      if (!existingWithCounter) {
        return uniqueSlug;
      }

      counter++;
      uniqueSlug = `${baseSlug}-${counter}`;
    }
  }
}
