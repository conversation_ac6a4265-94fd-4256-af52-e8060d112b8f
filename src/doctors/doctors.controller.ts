import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Req,
  UseGuards,
} from "@nestjs/common";
import { DoctorsService } from "./doctors.service";
import { CreateDoctorDto } from "./dto/create-doctor.dto";
import { UpdateDoctorDto } from "./dto/update-doctor.dto";
import { Paginate, PaginateQuery } from "nestjs-paginate";
import { FindAllDoctorsDto } from "./dto/find-all-doctors.dto";
import { ChangeDoctorStatusDto } from "./dto/change-doctor-status.dto";
import { AccessTokenGuard } from "src/common/guards/accessToken.guard";
import { ToggleAutoBookingDto } from "./dto/toggle-auto-booking.dto";
import { IsNull } from "typeorm";
import { DoctorType } from "./entities/doctor.entity";

@Controller("doctors")
export class DoctorsController {
  constructor(private readonly doctorsService: DoctorsService) {}

  @Post()
  create(@Body() createDoctorDto: CreateDoctorDto) {
    return this.doctorsService.create(createDoctorDto);
  }

  @Post("open")
  createOpen(@Body() createDoctorDto: CreateDoctorDto) {
    createDoctorDto.from_web = true;
    return this.doctorsService.create(createDoctorDto);
  }

  @Get()
  async findAll(
    @Paginate() query: PaginateQuery,
    @Query() filters: FindAllDoctorsDto,
  ) {
    return await this.doctorsService.findAllAdmin(query, filters);
  }

  @Get("open")
  async findAllOpen(
    @Paginate() query: PaginateQuery,
    @Query() filters: FindAllDoctorsDto,
  ) {
    filters.doctor_type = DoctorType.ONLINE;
    filters.status = 'active';
    return await this.doctorsService.findAll(query, filters);
  }

  @Get("home")
  async findAllHome(
    @Query() filters: FindAllDoctorsDto,
    @Paginate() query: PaginateQuery,
  ) {
    filters.status = 'active';
    return await this.doctorsService.findAll(query, filters);
  }

  @Get("user")
  findAllUser(
    @Paginate() query: PaginateQuery,
    @Query() filters: FindAllDoctorsDto,
  ) {
    filters.status = "active";
    return this.doctorsService.findAll(query, filters);
  }

  @Get("me")
  @UseGuards(AccessTokenGuard)
  async me(@Req() req: Request) {
    return this.doctorsService.me(req["uid"]);
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.doctorsService.findOne(id);
  }

  @Get(":id/user")
  findOneUser(@Param("id") id: string) {
    return this.doctorsService.findOneUser(id);
  }

  @Get("slug/:slug")
  findBySlug(@Param("slug") slug: string) {
    return this.doctorsService.findBySlug(slug);
  }

  @Patch(":id")
  update(@Param("id") id: string, @Body() updateDoctorDto: UpdateDoctorDto) {
    return this.doctorsService.update(id, updateDoctorDto);
  }

  @Patch(":id/change-status")
  async changeStatus(
    @Param("id") id: string,
    @Body() changeDoctorStatusDto: ChangeDoctorStatusDto,
  ) {
    return await this.doctorsService.changeStatus(id, changeDoctorStatusDto);
  }

  @Patch(":id/toggle-auto-booking")
  async toggleAutoBooking(
    @Param("id") id: string,
    @Body() toggleDto: ToggleAutoBookingDto,
  ) {
    const doctor = await this.doctorsService.toggleAutoBooking(
      id,
      toggleDto.enabled,
    );
    return {
      status: true,
      message: "Auto booking setting updated successfully",
      data: doctor,
    };
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.doctorsService.remove(id);
  }
}
