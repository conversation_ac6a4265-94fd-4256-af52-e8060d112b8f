import { forwardRef, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { TimeSlot } from "./entities/time-slot.entity";
import { CommonModule } from "../common/common.module";
import { DoctorsModule } from "src/doctors/doctors.module";
import { HospitalsModule } from "src/hospitals/hospitals.module";
import { TimeSlotController } from "./time-slots.controller";
import { TimeSlotService } from "./time-slots.service";
import { AvailabilitiesModule } from "src/availabilities/availabilities.module";
import { WeeksModule } from "src/weeks/weeks.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([TimeSlot]),
    CommonModule,
    forwardRef(() => DoctorsModule),
    DoctorsModule,
    HospitalsModule,
    AvailabilitiesModule,
    WeeksModule,
  ],
  controllers: [TimeSlotController],
  providers: [TimeSlotService],
  exports: [TimeSlotService],
})
export class TimeSlotModule {}
