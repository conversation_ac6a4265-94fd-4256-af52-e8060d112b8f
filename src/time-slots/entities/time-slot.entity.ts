import { Doctor } from "src/doctors/entities/doctor.entity";
import { Hospital } from "src/hospitals/entities/hospital.entity";
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
} from "typeorm";

@Entity("time_slots")
export class TimeSlot {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column()
  doctor_id: string;

  @Column()
  hospital_id: string;

  @ManyToOne(() => Doctor)
  @JoinColumn({ name: "doctor_id" })
  doctor: Doctor;

  @ManyToOne(() => Hospital)
  @JoinColumn({ name: "hospital_id" })
  hospital: Hospital;

  @Column({ type: "date" })
  date: Date;

  @Column({ type: "time" })
  startTime: string;

  @Column({ type: "time" })
  endTime: string;

  @Column({ default: true })
  isAvailable: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
