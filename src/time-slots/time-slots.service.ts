import { forwardRef, Inject, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, Between } from "typeorm";
import { TimeSlot } from "./entities/time-slot.entity";
import { ResponseService } from "../common/services/response.service";
import * as moment from "moment";
import { DoctorsService } from "src/doctors/doctors.service";
import { HospitalsService } from "src/hospitals/hospitals.service";
import { GetAvailableSlotsDto } from "./dto/get-available-slots";
import { format, getWeek } from "date-fns";
import { AvailabilitiesService } from "src/availabilities/availabilities.service";
import { Availability } from "src/availabilities/entities/availability.entity";
import { WeeksService } from "src/weeks/weeks.service";
import { Doctor } from "src/doctors/entities/doctor.entity";

@Injectable()
export class TimeSlotService {
  constructor(
    @InjectRepository(TimeSlot)
    private timeSlotRepository: Repository<TimeSlot>,
    private responseService: ResponseService,
    @Inject(forwardRef(() => DoctorsService))
    private doctorService: DoctorsService,
    private hospitalService: HospitalsService,
    private availabilitiesService: AvailabilitiesService,
    private weeksService: WeeksService,
  ) {}

  /**
   * Generates time slots for a specific date in 15-minute intervals
   */
  private generateTimeSlots(
    startTime: string = "09:00",
    endTime: string = "17:00",
    duration: number = 15,
  ) {
    const slots = [];
    let current = moment(startTime, "HH:mm:ss");
    const end = moment(endTime, "HH:mm:ss");

    while (current < end) {
      slots.push({
        startTime: current.format("HH:mm:ss"),
        endTime: current.add(duration, "minutes").format("HH:mm:ss"),
        isAvailable: true,
      });
    }

    return slots;
  }

  /**
   * Gets available time slots for a specific doctor, hospital and date
   */
  async getAvailableSlots(getAvailableSlotsDto: GetAvailableSlotsDto) {
    try {
      const { doctor_id, hospital_id, date } = getAvailableSlotsDto;
      // Check if doctor exists
      const doctor = await this.doctorService.findById(doctor_id);
      // console.log(doctor)
      if (!doctor) {
        return this.responseService.errorResponse("Doctor not found");
      }

      // Check if hospital exists
      // const hospital = await this.hospitalService.findById(hospital_id);
      // if (!hospital) {
      //   return this.responseService.errorResponse("Hospital not found");
      // }

      // Get all booked slots for the date
      const bookedSlots = await this.timeSlotRepository.find({
        where: {
          doctor_id,
          date,
          isAvailable: false,
        },
        select: ["startTime", "endTime"],
      });

      let startTime = "09:00";
      let endTime = "17:00";
      let notAvailable = false;
      let availabilities =
        await this.availabilitiesService.findDoctorAvailabilities(doctor_id, {
          date,
        });
      if (availabilities.length > 0) {
        notAvailable = availabilities.some(
          (item) => item.not_available === true,
        );
      } else {
        const week = await this.weeksService.findOneByWeekName(
          format(new Date(date), "EEEE"),
        );
        availabilities =
          await this.availabilitiesService.findDoctorAvailabilities(doctor_id, {
            week_id: week.id,
          });
      }

      if (!notAvailable && availabilities.length > 0) {
        startTime = availabilities.reduce(
          (min, item) => (item.startTime < min ? item.startTime : min),
          availabilities[0].startTime,
        );

        endTime = availabilities.reduce(
          (max, item) => (item.endTime > max ? item.endTime : max),
          availabilities[0].endTime,
        );
      }
      // return availabilities
      // Generate all possible slots
      // console.log('startTime', startTime)
      // console.log('endTime', endTime)
      // console.log('availabilities', availabilities)

      // console.log(bookedSlots)
      const duration = doctor.consultation_duration;
      const allSlots = this.generateTimeSlots(startTime, endTime, duration);

      // Mark booked slots as unavailable
      const now = moment();

      const availableSlots = await Promise.all(
        allSlots.map(async (slot) => {
          if (notAvailable) {
            return { ...slot, isAvailable: false };
          }
          const isWithinAvailability =
            availabilities.length > 0
              ? await this.checkAvailabilityBetweenTimes(
                  slot.startTime,
                  slot.endTime,
                  availabilities,
                )
              : false;
          const isBooked = bookedSlots.some(
            (bookedSlot) => bookedSlot.startTime === slot.startTime,
          );

          // Combine date and slot.startTime for comparison
          const slotDateTime = moment(`${date}T${slot.startTime}`);

          // If slot start time is in the past, mark as unavailable
          const isPast = slotDateTime.isBefore(now);

          return {
            ...slot,
            isAvailable: isWithinAvailability && !isBooked && !isPast,
          };
        }),
      );

      return this.responseService.successResponse(
        "Time slots retrieved successfully",
        availableSlots,
      );
    } catch (error) {
      return this.responseService.errorResponse(
        "Failed to get time slots: " + error.message,
      );
    }
  }

  /**
   * Marks a specific time slot as booked
   */
  async markSlotAsBooked(
    doctorId: string,
    // hospitalId: string,
    date: Date,
    startTime: string,
  ) {
    try {
      const doctor = await this.doctorService.findById(doctorId);
      const endTime = moment(startTime, "HH:mm")
        .add(doctor.consultation_duration, "minutes")
        .format("HH:mm");

      let timeSlot = await this.timeSlotRepository.findOne({
        where: {
          doctor: { id: doctorId },
          // hospital: { id: hospitalId },
          date: new Date(date),
          startTime,
        },
      });

      if (!timeSlot) {
        timeSlot = this.timeSlotRepository.create({
          doctor: { id: doctorId },
          // hospital: { id: hospitalId },
          date: new Date(date),
          startTime,
          endTime,
          isAvailable: false,
        });
      } else {
        timeSlot.isAvailable = false;
      }

      await this.timeSlotRepository.save(timeSlot);
      return this.responseService.successResponse("Time slot marked as booked");
    } catch (error) {
      console.log("Failed to mark slot as booked: " + error.message);
      return this.responseService.errorResponse(
        "Failed to mark slot as booked: " + error.message,
      );
    }
  }

  /**
   * Checks if a specific time slot is available
   */
  async isSlotAvailable(
    doctorId: string,
    hospitalId: string,
    date: string,
    startTime: string,
  ) {
    try {
      const timeSlot = await this.timeSlotRepository.findOne({
        where: {
          doctor: { id: doctorId },
          hospital: { id: hospitalId },
          date: new Date(date),
          startTime,
          isAvailable: false,
        },
      });

      return this.responseService.successResponse(
        "Time slot availability checked",
        !timeSlot,
      );
    } catch (error) {
      return this.responseService.errorResponse(
        "Failed to check slot availability: " + error.message,
      );
    }
  }

  async checkSlotAvailability(
    doctorId: string,
    hospitalId: string,
    date: Date,
    timeSlot: string,
  ): Promise<boolean> {
    try {
      const now = new Date();
      const slotDateTime = new Date(`${date}T${timeSlot}`);
      if (slotDateTime < now) {
        return false;
      }
      const existingBooking = await this.timeSlotRepository.findOne({
        where: {
          doctor_id: doctorId,
          date: date,
          startTime: timeSlot,
          isAvailable: false,
        },
      });
      // console.log(existingBooking)
      return !existingBooking;
    } catch (error) {
      console.log(`Failed to check slot availability: ${error.message}`);

      return false;
    }
  }

  async getAvailableSlotsDoctors(doctor_id: string) {
    const doctor = await this.doctorService.findById(doctor_id);
    if (!doctor) {
      return this.responseService.errorResponse("Doctor not found");
    }

    const startTime = "00:00";
    const endTime = "23:59";
    const duration = doctor.consultation_duration;
    const allSlots = this.generateTimeSlots(startTime, endTime, duration);
    return this.responseService.successResponse(
      "Time slots retrieved successfully",
      allSlots,
    );
  }

  async checkAvailabilityBetweenTimes(
    inputStart: string,
    inputEnd: string,
    availabilities: Availability[],
  ): Promise<boolean> {
    for (const slot of availabilities) {
      const slotStart = new Date(`1970-01-01T${slot.startTime}`);
      const slotEnd = new Date(`1970-01-01T${slot.endTime}`);
      const inputStartTime = new Date(`1970-01-01T${inputStart}`);
      const inputEndTime = new Date(`1970-01-01T${inputEnd}`);

      const isOverlap = inputStartTime < slotEnd && inputEndTime > slotStart;
      if (isOverlap) return true;
    }

    return false;
  }

  async findFirstAvailableSlot(doctor: Doctor, date: Date) {
    const doctor_id = doctor.id;
    // Step 2: Get booked slots
    const bookedSlots = await this.timeSlotRepository.find({
      where: { doctor_id, date, isAvailable: false },
      select: ["startTime", "endTime"],
    });

    // Step 3: Get availability
    let startTime = "09:00";
    let endTime = "17:00";
    let notAvailable = false;
    // console.log("1")
    let availabilities =
      await this.availabilitiesService.findDoctorAvailabilities(doctor_id, {
        date,
      });

    if (availabilities.length > 0) {
      notAvailable = availabilities.some((item) => item.not_available === true);
    } else {
      const week = await this.weeksService.findOneByWeekName(
        format(new Date(date), "EEEE"),
      );
      availabilities =
        await this.availabilitiesService.findDoctorAvailabilities(doctor_id, {
          week_id: week.id,
        });
    }

    if (notAvailable) {
      return null; // Doctor not available that day
    }

    startTime = availabilities.reduce(
      (min, item) => (item.startTime < min ? item.startTime : min),
      availabilities[0].startTime,
    );

    endTime = availabilities.reduce(
      (max, item) => (item.endTime > max ? item.endTime : max),
      availabilities[0].endTime,
    );

    // Step 4: Generate time slots
    const duration = doctor.consultation_duration;
    const allSlots = this.generateTimeSlots(startTime, endTime, duration);
    console.log(allSlots);

    // Step 5: Filter available slots
    const now = moment();
    for (const slot of allSlots) {
      const slotDateTime = moment(`${date}T${slot.startTime}`);
      const isPast = slotDateTime.isBefore(now);

      const isBooked = bookedSlots.some(
        (bs) => bs.startTime === slot.startTime,
      );
      const isWithinAvailability = await this.checkAvailabilityBetweenTimes(
        slot.startTime,
        slot.endTime,
        availabilities,
      );

      if (!isPast && !isBooked && isWithinAvailability) {
        return slot;
      }
    }
    return null; // No available slot found
  }
}
