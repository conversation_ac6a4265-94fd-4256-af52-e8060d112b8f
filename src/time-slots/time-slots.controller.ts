import { Controller, Get, Param, Query, UseGuards } from "@nestjs/common";
import { TimeSlotService } from "./time-slots.service";
import { GetAvailableSlotsDto } from "./dto/get-available-slots";

// import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
// import { RolesGuard } from '../../common/guards/roles.guard';
// import { Roles } from '../../common/decorators/roles.decorator';

@Controller("time-slots")
// @UseGuards(JwtAuthGuard, RolesGuard)
export class TimeSlotController {
  constructor(private readonly timeSlotService: TimeSlotService) {}

  @Get("available")
  // @Roles('USER', 'DOCTOR', 'HOSPITAL_ADMIN')
  async getAvailableSlots(@Query() getAvailableSlotsDto: GetAvailableSlotsDto) {
    return this.timeSlotService.getAvailableSlots(getAvailableSlotsDto);
  }

  @Get("doctors/:id")
  // @Roles('USER', 'DOCTOR', 'HOSPITAL_ADMIN')
  async getAvailableSlotsDoctors(@Param("id") id: string) {
    return this.timeSlotService.getAvailableSlotsDoctors(id);
  }
}
