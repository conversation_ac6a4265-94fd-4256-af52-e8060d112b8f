import { FindOptionsWhere, Repository } from "typeorm";

interface Props<T> {
  repository: Repository<T>;
  value: T[keyof T];
  key?: keyof T;
}

export async function recordExists<T>({
  repository,
  value,
  key,
}: Props<T>): Promise<boolean> {
  const whereClause = key
    ? ({ [key]: value } as FindOptionsWhere<T>)
    : ({ id: value } as unknown as FindOptionsWhere<T>);
  const isExists = await repository.findOne({
    where: whereClause,
  });
  return Boolean(isExists);
}
