import { Injectable } from "@nestjs/common";
import { CreateDoctorSpecialisationDto } from "./dto/create-doctor_specialisation.dto";
import { UpdateDoctorSpecialisationDto } from "./dto/update-doctor_specialisation.dto";
import { InjectRepository } from "@nestjs/typeorm";
import { DoctorSpecialisation } from "./entities/doctor_specialisation.entity";
import { Repository } from "typeorm";

@Injectable()
export class DoctorSpecialisationService {
  constructor(
    @InjectRepository(DoctorSpecialisation)
    private readonly doctorSpecialisation: Repository<DoctorSpecialisation>,
  ) {}
  async addSpecialisationToUsers(
    specialisations: { user_id: string; specialisation_id: string }[],
  ) {
    specialisations.map(async (spec) => {
      await this.doctorSpecialisation.insert(spec);
    });
  }

  create(createDoctorSpecialisationDto: CreateDoctorSpecialisationDto) {
    return "This action adds a new doctorSpecialisation";
  }

  findAll() {
    return `This action returns all doctorSpecialisation`;
  }

  findOne(id: number) {
    return `This action returns a #${id} doctorSpecialisation`;
  }

  update(
    id: number,
    updateDoctorSpecialisationDto: UpdateDoctorSpecialisationDto,
  ) {
    return `This action updates a #${id} doctorSpecialisation`;
  }

  remove(id: number) {
    return `This action removes a #${id} doctorSpecialisation`;
  }
}
