import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { DoctorSpecialisationService } from "./doctor_specialisation.service";
import { DoctorSpecialisationController } from "./doctor_specialisation.controller";
import { TypeOrmModule } from "@nestjs/typeorm";
import { DoctorSpecialisation } from "./entities/doctor_specialisation.entity";

@Module({
  imports: [TypeOrmModule.forFeature([DoctorSpecialisation])],
  controllers: [DoctorSpecialisationController],
  providers: [DoctorSpecialisationService],
  exports: [DoctorSpecialisationService, TypeOrmModule],
})
export class DoctorSpecialisationModule {}
