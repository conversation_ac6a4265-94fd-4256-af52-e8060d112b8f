import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from "@nestjs/common";
import { DoctorSpecialisationService } from "./doctor_specialisation.service";
import { CreateDoctorSpecialisationDto } from "./dto/create-doctor_specialisation.dto";
import { UpdateDoctorSpecialisationDto } from "./dto/update-doctor_specialisation.dto";

@Controller("doctor-specialisation")
export class DoctorSpecialisationController {
  constructor(
    private readonly doctorSpecialisationService: DoctorSpecialisationService,
  ) {}

  @Post()
  create(@Body() createDoctorSpecialisationDto: CreateDoctorSpecialisationDto) {
    return this.doctorSpecialisationService.create(
      createDoctorSpecialisationDto,
    );
  }

  @Get()
  findAll() {
    return this.doctorSpecialisationService.findAll();
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.doctorSpecialisationService.findOne(+id);
  }

  @Patch(":id")
  update(
    @Param("id") id: string,
    @Body() updateDoctorSpecialisationDto: UpdateDoctorSpecialisationDto,
  ) {
    return this.doctorSpecialisationService.update(
      +id,
      updateDoctorSpecialisationDto,
    );
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.doctorSpecialisationService.remove(+id);
  }
}
