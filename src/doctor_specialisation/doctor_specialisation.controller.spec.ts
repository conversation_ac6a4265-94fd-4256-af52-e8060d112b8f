import { Test, TestingModule } from "@nestjs/testing";
import { DoctorSpecialisationController } from "./doctor_specialisation.controller";
import { DoctorSpecialisationService } from "./doctor_specialisation.service";

describe("DoctorSpecialisationController", () => {
  let controller: DoctorSpecialisationController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DoctorSpecialisationController],
      providers: [DoctorSpecialisationService],
    }).compile();

    controller = module.get<DoctorSpecialisationController>(
      DoctorSpecialisationController,
    );
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });
});
