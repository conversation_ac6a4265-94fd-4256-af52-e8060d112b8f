import {
  BadRequestException,
  forwardRef,
  HttpStatus,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from "@nestjs/common";
import { CreateHospitalDto } from "./dto/create-hospital.dto";
import { UpdateHospitalDto } from "./dto/update-hospital.dto";
import { InjectRepository } from "@nestjs/typeorm";
import { Hospital } from "./entities/hospital.entity";
import { IsNull, Like, Not, Repository } from "typeorm";
import { CreateUserDto } from "src/users/dto/create-user.dto";
import { UsersService } from "src/users/users.service";
import { paginate, PaginateQuery } from "nestjs-paginate";
import { ResponseService } from "src/common/services/response.service";
import {
  ChangeHospitalStatusDto,
  UpdateAutoBookingDto,
} from "./dto/change-hospital-status.dto";
import { Review } from "src/reviews/entities/review.entity";
import { UpdateFastTagDto } from "./dto/update-fasttag.dto";
import { DocumentsService } from "src/documents/documents.service";
import { DoctorsService } from "src/doctors/doctors.service";
import { HospitalServicesService } from "src/hospital-services/hospital-services.service";

@Injectable()
export class HospitalsService {
  constructor(
    @InjectRepository(Hospital)
    private readonly hospitalRepository: Repository<Hospital>,
    @InjectRepository(Review)
    private readonly reviewRepository: Repository<Review>,
    private readonly userService: UsersService,
    private readonly responseService: ResponseService,
    private readonly documentsService: DocumentsService,
    @Inject(forwardRef(() => DoctorsService))
    private readonly doctorsService: DoctorsService,
    @Inject(forwardRef(() => HospitalServicesService))
    private readonly hospitalServicesService: HospitalServicesService,
  ) {}

  async create(createHospitalDto: CreateHospitalDto) {
    try {
      const { admin, ...rest } = createHospitalDto;
      const userExists = await this.userService.findOneByParam({
        email: admin.email,
      });
      if (userExists) {
        throw new BadRequestException("User already exists wit this email");
      }
      rest.slug = await this.generateUniqueSlug(rest.name);
      if (rest.from_web) {
        rest.status = "pending";
      }
      const hospital = this.hospitalRepository.create(rest);
      const savedHospital = await this.hospitalRepository.save(hospital);

      const createdUserRole = "admin";
      const createUserDto: CreateUserDto = {
        first_name: createHospitalDto.admin.name,
        last_name: "",
        user_type: "hsAdmin",
        email: admin.email,
        password: admin.password ?? "Password",
        is_active: rest.from_web
          ? false
          : createHospitalDto.status == "active"
            ? true
            : false, // Set is_active based on status
        hospital_id: savedHospital.id,
        role: createdUserRole,
        profile_picture: null,
      };
      const user = await this.userService.create(createUserDto);

      await this.userService.addRoleToUser(user, createdUserRole);
      if (createHospitalDto.documents) {
        await this.documentsService.createMultipleFiles({
          hospital_id: hospital.id,
          documents: createHospitalDto.documents,
        });
      }
      return {
        statusCode: HttpStatus.CREATED,
        message: "Hospital created successfully",
        data: hospital,
      };
    } catch (err) {
      console.log(err, "errorCheck");
      throw new InternalServerErrorException(
        `Failed to create hospital : ${err}`,
      );
    }
  }

  findAllAdmin(query: PaginateQuery, filters: any) {
    let where: any = {
      id: Not(IsNull()),
    };

    if (filters.name) {
      where.name = Like(`%${filters.name}%`);
    }

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.district_id) {
      where.district_id = filters.district_id;
    }

    return paginate(query, this.hospitalRepository, {
      sortableColumns: ["id"],
      where,
    });
  }

   findAll(query: PaginateQuery, filters: any) {
    let where: any = {
      id: Not(IsNull()),
    };

    if (filters.name) {
      where.name = Like(`%${filters.name}%`);
    }

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.district_id) {
      where.district_id = filters.district_id;
    }

    return paginate(query, this.hospitalRepository, {
      sortableColumns: ["id"],
      where,
    });
  }

  async findOne(id: string): Promise<Hospital> {
    const hospital = await this.hospitalRepository.findOne({ where: { id } });
    if (!hospital) {
      throw new NotFoundException(`Hospital with ID ${id} not found.`);
    }
    return hospital;
  }

  async findOneUser(id: string): Promise<any> {
    const hospital = await this.hospitalRepository.findOne({ where: { id } });
    if (!hospital) {
      throw new NotFoundException(`Hospital with ID ${id} not found.`);
    }
    return this.responseService.successResponse(
      "Hospital fetched successfully",
      hospital,
    );
  }

  async update(
    id: string,
    updateHospitalDto: UpdateHospitalDto,
  ): Promise<Hospital> {
    try {
      const { documents, ...data } = updateHospitalDto;
      const hospital = await this.hospitalRepository.findOne({
        where: { id },
      });
      if (!hospital) {
        throw new NotFoundException(`Hospital with ID ${id} not found`);
      }

      // Check if district_id is being updated
      if (data.district_id && data.district_id !== hospital.district_id) {
        // Update district_id for doctors associated with this hospital
        await this.userService.updateDistrictId(id, data.district_id);

        // Update district_id for hospital services associated with this hospital
        await this.hospitalServicesService.updateDistrictId(
          id,
          data.district_id,
        );
      }

      if (updateHospitalDto.documents) {
        await this.documentsService.createMultipleFiles({
          hospital_id: hospital.id,
          documents: updateHospitalDto.documents,
        });
      }

      return this.hospitalRepository.save({
        ...hospital,
        ...data,
      });
    } catch (err) {
      console.log(err, "errorCheck");
      throw new InternalServerErrorException(
        `Failed to update hospital : ${err}`,
      );
    }
  }

  async remove(id: string) {
    const result = await this.hospitalRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Hospital with ID ${id} not found`);
    }
    return {
      statusCode: HttpStatus.CREATED,
      message: "Hospital deleted successfully",
    };
  }

  async findById(id: string): Promise<Hospital> {
    const hospital = await this.hospitalRepository.findOne({ where: { id } });
    if (!hospital) {
      throw new NotFoundException(`Hospital with ID ${id} not found.`);
    }
    return hospital;
  }

  async findBySlug(slug: string): Promise<Hospital> {
    const hospital = await this.hospitalRepository.findOne({ where: { slug } });
    if (!hospital) {
      throw new NotFoundException(`Hospital with slug '${slug}' not found.`);
    }
    return hospital;
  }

  async changeStatus(
    id: string,
    changeHospitalStatusDto: ChangeHospitalStatusDto,
  ): Promise<any> {
    const hospital = await this.hospitalRepository.findOne({ where: { id } });

    if (!hospital) {
      throw new NotFoundException(`Hospital with ID ${id} not found`);
    }

    hospital.status = changeHospitalStatusDto.status;
    await this.hospitalRepository.save(hospital);
    if (changeHospitalStatusDto.status === "active") {
      // change hsAdmin is_active to true

      const hsAdmin = await this.userService.findOneByParam({
        hospital_id: id,
        user_type: "hsAdmin",
      });
      if (hsAdmin) {
        hsAdmin.is_active = true;
        await this.userService.saveInstance(hsAdmin);
      }
    } else {
      // change hsAdmin is_active to false
      const hsAdmin = await this.userService.findOneByParam({
        hospital_id: id,
        user_type: "hsAdmin",
      });
      if (hsAdmin) {
        hsAdmin.is_active = false;
        await this.userService.saveInstance(hsAdmin);
      }
    }
    return {
      status: true,
      message: "Hospital status updated successfully",
    };
  }

  async calculateRatings(hospital_id: string): Promise<void> {
    const reviews = await this.reviewRepository.find({
      where: { hospital_id },
      select: ["rating"],
    });

    if (!reviews.length) {
      await this.hospitalRepository.update(hospital_id, {
        total_rating: 0,
        avg_rating: 0,
        rating_count: 0,
      });
      return;
    }

    const totalRating = reviews.reduce(
      (sum, review) => sum + Number(review.rating),
      0,
    );
    const ratingCount = reviews.length;
    const avgRating = totalRating / ratingCount;

    await this.hospitalRepository.update(hospital_id, {
      total_rating: totalRating,
      avg_rating: parseFloat(avgRating.toFixed(2)),
      rating_count: ratingCount,
    });
  }

  async updateFastTag(
    id: string,
    updateFastTagDto: UpdateFastTagDto,
  ): Promise<any> {
    const hospital = await this.hospitalRepository.findOne({ where: { id } });

    if (!hospital) {
      throw new NotFoundException(`Hospital with ID ${id} not found`);
    }

    hospital.fastTag = {
      enabled: updateFastTagDto.enabled,
      count: updateFastTagDto.count,
      price: updateFastTagDto.price,
    };

    await this.hospitalRepository.save(hospital);

    return {
      status: true,
      message: "Fast Tag updated successfully",
      data: hospital.fastTag,
    };
  }

  async updateAutoBooking(
    id: string,
    UpdateAutoBookingDto: UpdateAutoBookingDto,
  ): Promise<any> {
    const hospital = await this.hospitalRepository.findOne({ where: { id } });

    if (!hospital) {
      throw new NotFoundException(`Hospital with ID ${id} not found`);
    }

    hospital.auto_booking_enabled = UpdateAutoBookingDto.auto_booking_enabled;
    await this.hospitalRepository.save(hospital);

    return {
      status: true,
      message: "Hospital auto booking updated successfully",
    };
  }

  async generateUniqueSlug(name: string): Promise<string> {
    // Create base slug by converting to lowercase and replacing spaces with underscores
    const baseSlug = name
      .toLowerCase()
      .replace(/\s+/g, "_")
      .replace(/[^a-z0-9_]/g, "");
    // Check if the base slug exists
    const existingHospital = await this.hospitalRepository.findOne({
      where: { slug: baseSlug },
    });

    if (!existingHospital) {
      return baseSlug;
    }

    // If base slug exists, find the next available number
    let counter = 1;
    let uniqueSlug = `${baseSlug}-${counter}`;

    while (true) {
      const existingWithCounter = await this.hospitalRepository.findOne({
        where: { slug: uniqueSlug },
      });

      if (!existingWithCounter) {
        return uniqueSlug;
      }

      counter++;
      uniqueSlug = `${baseSlug}-${counter}`;
    }
  }
}
