import { forwardRef, Module } from "@nestjs/common";
import { HospitalsService } from "./hospitals.service";
import { HospitalsController } from "./hospitals.controller";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Hospital } from "./entities/hospital.entity";
import { UsersModule } from "src/users/users.module";
import { CommonModule } from "src/common/common.module";
import { Review } from "src/reviews/entities/review.entity";
import { DocumentsModule } from "src/documents/documents.module";
import { DoctorsModule } from "src/doctors/doctors.module";
import { HospitalServicesModule } from "src/hospital-services/hospital-services.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([Hospital, Review]),
    UsersModule,
    CommonModule,
    DocumentsModule,
    forwardRef(() => DoctorsModule),
    forwardRef(() => HospitalServicesModule),
  ],
  controllers: [HospitalsController],
  providers: [HospitalsService],
  exports: [HospitalsService],
})
export class HospitalsModule {}
