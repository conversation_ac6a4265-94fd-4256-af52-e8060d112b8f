import { District } from "src/districts/entities/districts.entity";
import { Doctor } from "src/doctors/entities/doctor.entity";
import { BankDetails } from "src/types/doctors.type";
import { HospitalAddress } from "src/types/hospital.type";
import { Review } from "src/reviews/entities/review.entity";
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from "typeorm";
import { Transaction } from "src/transactions/entities/transaction.entity";
import { Wallet } from "src/wallet/entities/wallet.entity";

@Entity({ name: "hospitals" })
export class Hospital {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ nullable: false })
  name: string;

  @Column({ nullable: false })
  logo: string;

  @Column({ nullable: false })
  location: string;

  @Column("simple-json", { nullable: false })
  address: HospitalAddress;

  @Column("simple-json", { nullable: false })
  contact_details: any;

  @Column("simple-json", { nullable: false })
  billing_address: HospitalAddress;

  @Column({ nullable: false })
  gst: string;

  @Column({ nullable: true })
  website: string;

  @Column("simple-json", { nullable: true })
  ratings: { userID: string; rating: number; submittedOn: Date }[];

  @Column("simple-json", { nullable: true })
  feedbacks: { userID: string; remark: string; submittedOn: Date }[];

  @Column({ type: "uuid", nullable: true })
  parent_id: string | null;

  // @Column("simple-json", { nullable: true })
  // documents: Record<string, string>;

  @Column("simple-json", { nullable: true })
  fastTag: { enabled: boolean; count: number; price: number };

  @Column("simple-json", { nullable: true })
  departments: string[];

  @Column("enum", {
    enum: ["pending", "active", "disabled", "rejected"],
    nullable: true,
  })
  status: string;

  @Column({ type: "boolean", default: false })
  isDisabled: boolean;

  @Column({ type: "boolean", default: false })
  from_web: boolean;

  @Column({ nullable: true })
  slug: string;

  @Column({ type: "boolean", default: false })
  isDeactivated: boolean;

  @Column("uuid", { nullable: true })
  district_id: string | null;

  @ManyToOne(() => District, (district) => district.hospitals, {
    nullable: true,
  })
  @JoinColumn({ name: "district_id" })
  district?: District;

  @OneToMany(() => Doctor, (doctor) => doctor.hospital, {
    cascade: true,
  })
  doctors: Doctor[];

  @Column({ type: "boolean", default: false })
  is_fast_tag_enabled: boolean;

  @Column({ type: "boolean", default: false })
  auto_booking_enabled: boolean;

  @Column("simple-json", { nullable: true })
  bank_details: BankDetails;

  @OneToMany(() => Review, (review) => review.hospital)
  reviews: Review[];

  @Column({ type: "decimal", precision: 10, scale: 2, default: 0 })
  total_rating: number;

  @Column({ type: "decimal", precision: 3, scale: 2, default: 0 })
  avg_rating: number;

  @Column({ type: "int", default: 0 })
  rating_count: number;

  @OneToMany(() => Transaction, (review) => review.hospital)
  transactions: Transaction[];

  @OneToMany(() => Wallet, (wallet) => wallet.hospital)
  wallets: Wallet[];

  @CreateDateColumn()
  created_at: Date;
}
