import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from "@nestjs/common";
import { HospitalsService } from "./hospitals.service";
import { CreateHospitalDto } from "./dto/create-hospital.dto";
import { UpdateHospitalDto } from "./dto/update-hospital.dto";
import { Paginate, PaginateQuery } from "nestjs-paginate";
import {
  ChangeHospitalStatusDto,
  UpdateAutoBookingDto,
} from "./dto/change-hospital-status.dto";
import { UpdateFastTagDto } from "./dto/update-fasttag.dto";

@Controller("hospitals")
export class HospitalsController {
  constructor(private readonly hospitalsService: HospitalsService) { }

  @Post()
  create(@Body() createHospitalDto: CreateHospitalDto) {
    return this.hospitalsService.create(createHospitalDto);
  }

  @Post("open")
  createOpen(@Body() createHospitalDto: CreateHospitalDto) {
    createHospitalDto.from_web = true;
    return this.hospitalsService.create(createHospitalDto);
  }

  @Get()
  findAll(@Paginate() query: PaginateQuery, @Query() filters: any) {
    return this.hospitalsService.findAll(query, filters);
  }

  @Get("user")
  findAllUser(@Paginate() query: PaginateQuery, @Query() filters: any) {
    filters.status = "active";
    return this.hospitalsService.findAll(query, filters);
  }

  @Get("open")
  findAllOpen(@Paginate() query: PaginateQuery, @Query() filters: any) {
    filters.status = "active";
    return this.hospitalsService.findAll(query, filters);
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.hospitalsService.findOne(id);
  }

  @Get(":id/user")
  findOneUser(@Param("id") id: string) {
    return this.hospitalsService.findOneUser(id);
  }

  @Patch(":id")
  update(
    @Param("id") id: string,
    @Body() updateHospitalDto: UpdateHospitalDto,
  ) {
    return this.hospitalsService.update(id, updateHospitalDto);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.hospitalsService.remove(id);
  }

  @Patch(":id/change-status")
  async changeStatus(
    @Param("id") id: string,
    @Body() changeHospitalStatusDto: ChangeHospitalStatusDto,
  ) {
    return await this.hospitalsService.changeStatus(
      id,
      changeHospitalStatusDto,
    );
  }
  @Patch(":id/update-auto-booking")
  async updateAutoBooking(
    @Param("id") id: string,
    @Body() UpdateAutoBookingDto: UpdateAutoBookingDto,
  ) {
    return await this.hospitalsService.updateAutoBooking(
      id,
      UpdateAutoBookingDto,
    );
  }

  @Patch(":id/update-fast-tag")
  async updateFastTag(
    @Param("id") id: string,
    @Body() updateFastTagDto: UpdateFastTagDto,
  ) {
    return await this.hospitalsService.updateFastTag(id, updateFastTagDto);
  }

  @Get("slug/:slug")
  findBySlug(@Param("slug") slug: string) {
    return this.hospitalsService.findBySlug(slug);
  }
}
