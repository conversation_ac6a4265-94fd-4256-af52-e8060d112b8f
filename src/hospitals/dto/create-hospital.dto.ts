import { Type } from "class-transformer";
import {
  <PERSON><PERSON>rray,
  IsBoolean,
  IsIn,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from "class-validator";
import { FileDto } from "src/documents/dto/create-multiple-files.dto";
import { BankDetails } from "src/types/doctors.type";
import { AdminRoles } from "src/types/hospital.type";

export class HospitalAddress {
  @IsString()
  lineOne: string;

  @IsString()
  lineTwo: string;

  @IsString()
  city: string;

  @IsString()
  district: string;

  @IsString()
  state: string;

  @IsString()
  pincode: string;

  @IsString()
  street: string;
}

export class Admin {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  role?: AdminRoles;

  @IsString()
  email: string;

  @IsString()
  password: string;
}

export class Rating {
  @IsUUID()
  userID: string;

  @IsString()
  rating: number;

  @IsString()
  submittedOn: Date;
}

export class Feedback {
  @IsUUID()
  userID: string;

  @IsString()
  remark: string;

  @IsString()
  submittedOn: Date;
}

export class FastTag {
  @IsBoolean()
  enabled: boolean;

  @IsNumber()
  count: number;

  @IsNumber()
  price: number;
}

export class CreateHospitalDto {
  @IsString()
  name: string;

  @IsString()
  logo: string;

  @IsString()
  location: string;

  @IsObject()
  @ValidateNested()
  @Type(() => HospitalAddress)
  address: HospitalAddress;

  @IsObject()
  @ValidateNested()
  @Type(() => HospitalAddress)
  billing_address: HospitalAddress;

  @IsOptional()
  @IsObject()
  @ValidateNested({ each: true })
  @Type(() => Admin)
  admin: Admin;

  @IsOptional()
  @IsString()
  gst: string;

  @IsOptional()
  @IsString()
  about: string;

  @IsOptional()
  @IsString()
  website?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Rating)
  ratings?: Rating[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Feedback)
  feedbacks?: Feedback[];

  @IsOptional()
  @IsUUID()
  parent_id?: string;

  @IsArray()
  @ValidateNested({ each: true }) // Validate each object in the array
  @Type(() => FileDto) // Transform each item in the array to an instance of FileDto
  documents?: FileDto[];

  @IsObject()
  @ValidateNested()
  @Type(() => FastTag)
  fastTag?: { enabled: boolean; count: number; price: number };

  @IsArray()
  @IsOptional()
  departments?: string[];

  @IsString()
  @IsOptional()
  @IsIn(["active", "pending", "rejected"])
  status: "active" | "pending" | "rejected";

  @IsString()
  @IsOptional()
  slug: string;

  @IsBoolean()
  @IsOptional()
  isDisabled: boolean;

  @IsBoolean()
  @IsOptional()
  from_web: boolean;

  @IsBoolean()
  @IsOptional()
  isDeactivated: boolean;

  @IsObject()
  @IsOptional()
  bank_details?: BankDetails;

  @IsOptional()
  district_id?: string;

  @IsOptional()
  @IsObject()
  contact_details: any;
}
