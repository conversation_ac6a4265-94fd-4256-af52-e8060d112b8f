import { Injectable } from "@nestjs/common";
import { Coupon } from "../entities/coupon.entity";

@Injectable()
export class CouponUsageService {
  async calculateCouponDiscount(
    coupon: Coupon,
    amount: number,
  ): Promise<number> {
    switch (coupon.discount_type) {
      case "percentage":
        return this.applyPercentageDiscount(coupon, amount);
      case "flat":
        return this.applyFlatDiscount(coupon);
      default:
        return 0;
    }
  }

  private applyPercentageDiscount(coupon: Coupon, amount: number): number {
    const percentage = coupon.minimum_discount_allowed || 0;
    const discount = Math.round(((percentage * amount) / 100) * 100) / 100; // Round to 2 decimal places
    return discount;
  }

  private applyFlatDiscount(coupon: Coupon): number {
    const discount = coupon.minimum_discount_allowed || 0;
    return discount;
  }
}
