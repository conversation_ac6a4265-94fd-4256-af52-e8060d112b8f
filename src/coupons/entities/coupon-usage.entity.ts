import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
} from "typeorm";
import { Coupon } from "./coupon.entity";

@Entity("coupon_usage")
export class CouponUsage {
  @PrimaryGeneratedColumn("increment")
  id: number;

  @Column({ type: "varchar", unsigned: true })
  coupon_id: string;

  @ManyToOne(() => Coupon, (coupon) => coupon.usages, { onDelete: "CASCADE" })
  @JoinColumn({ name: "coupon_id" })
  coupon: Coupon;

  @Column({ type: "varchar" })
  user_id: string;
}
