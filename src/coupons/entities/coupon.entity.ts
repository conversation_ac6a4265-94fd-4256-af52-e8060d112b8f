import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from "typeorm";
import { CouponUsage } from "./coupon-usage.entity";

// Define the DiscountType enum
export enum DiscountType {
  FLAT = "flat",
  PERCENTAGE = "percentage",
}

@Entity("coupons")
export class Coupon {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 255 })
  coupon_code: string;

  @Column({ type: "enum", enum: DiscountType }) // Use the DiscountType enum
  discount_type: DiscountType;

  @Column({ type: "datetime" })
  valid_untill: Date;

  @Column({ type: "decimal", precision: 8, scale: 2, nullable: true })
  minimum_cart_amount: number;

  @Column({ type: "decimal", precision: 8, scale: 2, nullable: true })
  minimum_discount_allowed: number;

  @Column({ type: "int" })
  usage_limit: number;

  @Column({ type: "int", default: 1 })
  usage_limit_per_user: number;

  @Column({ type: "int", default: 0 })
  usage_count: number;

  @Column()
  hospital_id: string;

  @OneToMany(() => CouponUsage, (usage) => usage.coupon)
  usages: CouponUsage[];

  @DeleteDateColumn()
  deleted_at: Date;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
