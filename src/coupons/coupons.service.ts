import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Coupon } from "./entities/coupon.entity";
import { CouponUsage } from "./entities/coupon-usage.entity";
import { CreateCouponDto } from "./dto/create-coupon.dto";
import { PaginateQuery, paginate } from "nestjs-paginate";
import { CouponUsageService } from "./services/coupon-usage.service";

@Injectable()
export class CouponsService {
  constructor(
    @InjectRepository(Coupon)
    private readonly couponRepository: Repository<Coupon>,
    @InjectRepository(CouponUsage)
    private readonly couponUsageRepository: Repository<CouponUsage>,
    private readonly couponUsageService: CouponUsageService,
  ) {}

  async createCoupon(createCouponDto: CreateCouponDto): Promise<any> {
    const existingCoupon = await this.couponRepository.findOne({
      where: { coupon_code: createCouponDto.coupon_code },
    });
    if (existingCoupon) {
      throw new BadRequestException(
        `Coupon with code ${createCouponDto.coupon_code} already exists`,
      );
    }
    const coupon = this.couponRepository.create(createCouponDto);
    await this.couponRepository.save(coupon);
    return {
      status: true,
      message: "Coupon created successfully",
      data: coupon,
    };
  }

  async findAllCoupons(query: PaginateQuery): Promise<any> {
    return paginate(query, this.couponRepository, {
      sortableColumns: ["created_at"],
    });
  }

  async findCouponById(id: string): Promise<Coupon> {
    const coupon = await this.couponRepository.findOne({ where: { id } });
    if (!coupon) {
      throw new NotFoundException(`Coupon with ID ${id} not found`);
    }
    return coupon;
  }

  async updateCoupon(id: string, updateCouponDto: any): Promise<Coupon> {
    const coupon = await this.findCouponById(id);
    Object.assign(coupon, updateCouponDto);
    return await this.couponRepository.save(coupon);
  }

  async deleteCoupon(id: string): Promise<void> {
    const coupon = await this.findCouponById(id);
    await this.couponRepository.softRemove(coupon);
  }

  async validateCoupon(
    couponCode: string,
    userId: string,
    amount: number,
  ): Promise<any> {
    const coupon = await this.couponRepository.findOne({
      where: { coupon_code: couponCode },
    });

    if (!coupon) {
      throw new BadRequestException("Coupon is not valid");
    }

    // Check if the coupon is expired
    if (new Date(coupon.valid_untill) < new Date()) {
      throw new BadRequestException("Coupon expired.");
    }
    // Check total coupon usage limit
    if (coupon.usage_count >= coupon.usage_limit) {
      throw new BadRequestException("Coupon use limit is exceeded.");
    }

    // Check minimum cart amount
    if (amount < coupon.minimum_cart_amount) {
      throw new BadRequestException(
        `Coupon not applicable, minimum amount should be ${coupon.minimum_cart_amount}`,
      );
    }

    // Check user-specific usage limit
    const userTimesRedeemed = await this.couponUsageRepository.count({
      where: { coupon_id: coupon.id, user_id: userId },
    });
    if (userTimesRedeemed >= coupon.usage_limit_per_user) {
      throw new BadRequestException(
        "Coupon has already been used the maximum number of times by this user.",
      );
    }

    // Calculate the discount
    const discount = this.couponUsageService.calculateCouponDiscount(
      coupon,
      amount,
    );

    return {
      is_valid: true,
      message: "Coupon applied successfully",
      discount,
      coupon,
    };
  }

  async updateCouponUsageCount(
    couponId: string,
    userId: string,
  ): Promise<CouponUsage> {
    const couponUsage = this.couponUsageRepository.create({
      coupon_id: couponId,
      user_id: userId,
    });
    await this.couponUsageRepository.save(couponUsage);

    // Update the coupon's usage count
    const coupon = await this.couponRepository.findOne({
      where: { id: couponId },
    });
    if (coupon) {
      coupon.usage_count += 1;
      await this.couponRepository.save(coupon);
    }

    return couponUsage;
  }
}
