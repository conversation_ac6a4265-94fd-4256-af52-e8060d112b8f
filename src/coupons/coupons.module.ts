import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { CouponsService } from "./coupons.service";
import { CouponsController } from "./coupons.controller";
import { Coupon } from "./entities/coupon.entity";
import { CouponUsage } from "./entities/coupon-usage.entity";
import { CouponUsageService } from "./services/coupon-usage.service";

@Module({
  imports: [TypeOrmModule.forFeature([Coupon, CouponUsage])],
  controllers: [CouponsController],
  providers: [CouponsService, CouponUsageService],
  exports: [CouponsService, CouponUsageService],
})
export class CouponsModule {}
