import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON>ot<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "class-validator";

export class ValidateCouponDto {
  @IsNotEmpty()
  @IsString()
  coupon_code: string;

  @IsNotEmpty()
  @IsString()
  user_id: string;

  @IsNotEmpty()
  @IsNumber()
  amount: number;

  @IsOptional()
  hospital_id?: string;

  @IsOptional()
  doctor_id?: string;

  @IsOptional()
  service_id?: string;
}
