import {
  IsString,
  IsEnum,
  IsDate,
  IsO<PERSON>al,
  <PERSON><PERSON><PERSON>ber,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Int,
  IsNotEmpty,
} from "class-validator";
import { DiscountType } from "../entities/coupon.entity";

export class CreateCouponDto {
  @IsString()
  @MaxLength(255)
  @IsNotEmpty()
  coupon_code: string;

  @IsNotEmpty()
  @IsEnum(DiscountType)
  discount_type: DiscountType;

  @IsOptional()
  valid_untill: Date;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  minimum_cart_amount?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  minimum_discount_allowed?: number;

  @IsInt()
  @IsOptional()
  usage_limit: number;

  @IsOptional()
  @IsInt()
  usage_limit_per_user?: number;

  @IsString()
  @IsOptional()
  hospital_id: string;
}
