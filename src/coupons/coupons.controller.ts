import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
} from "@nestjs/common";
import { CouponsService } from "./coupons.service";
import { CreateCouponDto } from "./dto/create-coupon.dto";
import { ValidateCouponDto } from "./dto/validate-coupon.dto";
import { Paginate, PaginateQuery } from "nestjs-paginate";

@Controller("coupons")
export class CouponsController {
  constructor(private readonly couponsService: CouponsService) {}

  @Post()
  create(@Body() createCouponDto: CreateCouponDto) {
    return this.couponsService.createCoupon(createCouponDto);
  }

  @Get()
  findAll(@Paginate() query: PaginateQuery) {
    return this.couponsService.findAllCoupons(query);
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.couponsService.findCouponById(id);
  }

  @Patch(":id")
  update(@Param("id") id: string, @Body() updateCouponDto: any) {
    return this.couponsService.updateCoupon(id, updateCouponDto);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.couponsService.deleteCoupon(id);
  }

  @Post("validate")
  async validateCoupon(@Body() validateCouponDto: ValidateCouponDto) {
    return this.couponsService.validateCoupon(
      validateCouponDto.coupon_code,
      validateCouponDto.user_id,
      validateCouponDto.amount,
    );
  }
}
