import { Hospital } from "src/hospitals/entities/hospital.entity";
import { District } from "src/districts/entities/districts.entity";
import {
  Entity,
  PrimaryColumn,
  Column,
  ManyToOne,
  JoinColumn,
  PrimaryGeneratedColumn,
} from "typeorm";

@Entity("hospital_services")
export class HospitalService {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", nullable: false })
  name: string;

  @Column({ type: "varchar", nullable: true })
  description: string;

  @Column({ type: "varchar", nullable: true })
  image: string;

  @Column({ type: "decimal", precision: 10, scale: 2, nullable: true })
  price: number;

  @Column({ type: "decimal", precision: 10, scale: 2, nullable: true })
  strike_through_price: number;

  @Column({ type: "varchar", length: 36, nullable: false })
  hospital_id: string;

  @Column({ type: "uuid", nullable: true })
  district_id: string;

  @Column({ type: "int", nullable: true, default: 10 })
  daily_booking_count: number | null;

  @ManyToOne(() => Hospital, { onDelete: "CASCADE" })
  @JoinColumn({ name: "hospital_id" })
  hospital: Hospital;

  @ManyToOne(() => District, { onDelete: "SET NULL" })
  @JoinColumn({ name: "district_id" })
  district: District;
}
