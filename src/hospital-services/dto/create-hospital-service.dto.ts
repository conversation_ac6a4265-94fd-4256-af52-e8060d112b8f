import {
  IsOptional,
  IsString,
  IsUUID,
  IsDecimal,
  IsNumber,
} from "class-validator";

export class CreateHospitalServiceDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  image?: string;

  @IsOptional()
  @IsDecimal()
  price?: number;

  @IsOptional()
  @IsDecimal()
  strike_through_price?: number;

  @IsUUID()
  hospital_id: string;

  @IsOptional()
  @IsNumber()
  daily_booking_count?: number; // Add validation for daily_booking_count
}
