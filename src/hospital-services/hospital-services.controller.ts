import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UsePipes,
  ValidationPipe,
} from "@nestjs/common";
import { HospitalServicesService } from "./hospital-services.service";
import { CreateHospitalServiceDto } from "./dto/create-hospital-service.dto";
import { UpdateHospitalServiceDto } from "./dto/update-hospital-service.dto";
import { PaginationDto } from "src/common/dto/pagination.dto";
import { Paginate, PaginateQuery } from "nestjs-paginate";

@Controller("hospital-services")
export class HospitalServicesController {
  constructor(
    private readonly hospitalServicesService: HospitalServicesService,
  ) {}

  @Get()
  findAll(@Paginate() query: PaginateQuery, @Query() filters: any) {
    return this.hospitalServicesService.findAll(query, filters);
  }

  @Get("user")
  findAllUser(@Paginate() query: PaginateQuery, @Query() filters: any) {
    return this.hospitalServicesService.findAllUser(query, filters);
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.hospitalServicesService.findOne(id);
  }

  @Post()
  create(@Body() createHospitalServiceDto: CreateHospitalServiceDto) {
    return this.hospitalServicesService.create(createHospitalServiceDto);
  }

  @Put(":id")
  update(
    @Param("id") id: string,
    @Body() updateHospitalServiceDto: UpdateHospitalServiceDto,
  ) {
    return this.hospitalServicesService.update(id, updateHospitalServiceDto);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.hospitalServicesService.remove(id);
  }
}
