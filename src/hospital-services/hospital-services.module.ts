import { forwardRef, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { HospitalServicesController } from "./hospital-services.controller";
import { HospitalServicesService } from "./hospital-services.service";
import { HospitalService } from "./entities/hospital-service.entity";
import { CommonModule } from "src/common/common.module";
import { HospitalsModule } from "src/hospitals/hospitals.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([HospitalService]),
    CommonModule,
    forwardRef(() => HospitalsModule),
  ],
  controllers: [HospitalServicesController],
  providers: [HospitalServicesService],
  exports: [HospitalServicesService],
})
export class HospitalServicesModule {}
