import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { IsNull, Not, Repository } from "typeorm";
import { HospitalService } from "./entities/hospital-service.entity";
import { CreateHospitalServiceDto } from "./dto/create-hospital-service.dto";
import { UpdateHospitalServiceDto } from "./dto/update-hospital-service.dto";
import { ResponseService } from "src/common/services/response.service";
import { PaginationDto } from "src/common/dto/pagination.dto";
import { paginate, PaginateQuery } from "nestjs-paginate";
import { HospitalsService } from "src/hospitals/hospitals.service";

@Injectable()
export class HospitalServicesService {
  constructor(
    @InjectRepository(HospitalService)
    private readonly hospitalServicesRepository: Repository<HospitalService>,
    private readonly responseService: ResponseService,
    @Inject(forwardRef(() => HospitalsService))
    private readonly hospitalService: HospitalsService,
  ) {}

  findAll(query: PaginateQuery, filters: any) {
    let where: any = {
      id: Not(IsNull()),
    };

    if (filters.name) {
      where.name = filters.name;
    }

    if (filters.hospital_id) {
      where.hospital_id = filters.hospital_id;
    }

    if (filters.district_id) {
      where.district_id = filters.district_id;
    }
    return paginate(query, this.hospitalServicesRepository, {
      sortableColumns: ["name"],
      relations: ["hospital"],
      where,
    });
  }

  findAllUser(query: PaginateQuery, filters: any) {
    let where: any = {
      id: Not(IsNull()),
    };

    if (filters.name) {
      where.name = filters.name;
    }

    if (filters.hospital_id) {
      where.hospital_id = filters.hospital_id;
    }

    if (filters.district_id) {
      where.district_id = filters.district_id;
    }

    return paginate(query, this.hospitalServicesRepository, {
      sortableColumns: ["name"],
      relations: ["hospital"],
      where,
    });
  }

  async findOne(id: string) {
    const service = await this.hospitalServicesRepository.findOne({
      where: { id },
    });
    if (!service) {
      throw new NotFoundException(`Hospital service with ID ${id} not found`);
    }
    return this.responseService.successResponse(
      "Hospital service fetched successfully",
      service,
    );
  }

  async findOneById(id: string): Promise<HospitalService> {
    const service = await this.hospitalServicesRepository.findOne({
      where: { id },
    });
    if (!service) {
      throw new NotFoundException(`Hospital service with ID ${id} not found`);
    }
    return service;
  }

  async create(createHospitalServiceDto: CreateHospitalServiceDto) {
    const hospital = await this.hospitalService.findById(
      createHospitalServiceDto.hospital_id,
    );

    const service = this.hospitalServicesRepository.create({
      ...createHospitalServiceDto,
      district_id: hospital.district_id,
    });

    const newService = await this.hospitalServicesRepository.save(service);
    return this.responseService.successResponse(
      "Hospital service created successfully",
      newService,
    );
  }

  async update(id: string, updateHospitalServiceDto: UpdateHospitalServiceDto) {
    await this.hospitalServicesRepository.update(id, updateHospitalServiceDto);
    return this.responseService.successResponse(
      "Hospital service updated successfully",
    );
  }

  async updateDistrictId(hospital_id: string, district_id: string) {
    await this.hospitalServicesRepository.update(
      { hospital_id },
      { district_id },
    );
  }

  async remove(id: string) {
    await this.hospitalServicesRepository.delete(id);
    return this.responseService.successResponse(
      "Hospital service removed successfully",
    );
  }
}
