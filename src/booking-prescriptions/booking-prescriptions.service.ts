import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { BookingPrescription } from "./entities/booking-prescription.entity";
import { CreateBookingPrescriptionDto } from "./dto/create-booking-prescription.dto";
import { UpdateBookingPrescriptionDto } from "./dto/update-booking-prescription.dto";
import { BookingService } from "src/bookings/bookings.service";
import { paginate, PaginateQuery } from "nestjs-paginate";
import { FileUploadService } from "src/file-upload/file-upload.service";
import { BookingStatus } from "src/bookings/entities/booking.entity";

@Injectable()
export class BookingPrescriptionsService {
  constructor(
    @InjectRepository(BookingPrescription)
    private readonly bookingPrescriptionsRepository: Repository<BookingPrescription>,
    private readonly bookingService: BookingService, // Assuming BookingModule is imported correctly
    private readonly uploadService: FileUploadService, // Assuming uploadService is injected correctly
  ) {}

  create(createBookingPrescriptionDto: CreateBookingPrescriptionDto) {
    return "This action adds a new bookingPrescription";
  }

  async findAll(query: PaginateQuery, booking_id: string) {
    // Fetch paginated data
    const data = await paginate(query, this.bookingPrescriptionsRepository, {
      sortableColumns: ["id"],
      where: {
        booking_id,
      },
    });

    // Map through the results and replace file keys with signed URLs
    const updatedData = await Promise.all(
      data.data.map(async (prescription) => {
        const signedUrl = await this.uploadService.getSignedUrl(
          prescription.file,
        ); // Call the upload service
        return {
          ...prescription,
          file: signedUrl, // Replace the file key with the signed URL
        };
      }),
    );

    return {
      ...data,
      data: updatedData, // Replace the original data with updated data
    };
  }

  findOne(id: number) {
    return `This action returns a #${id} bookingPrescription`;
  }

  update(
    id: number,
    updateBookingPrescriptionDto: UpdateBookingPrescriptionDto,
  ) {
    return `This action updates a #${id} bookingPrescription`;
  }

  async remove(id: string) {
    const result = await this.bookingPrescriptionsRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(
        `Booking prescription with ID ${id} not found`,
      );
    }
    return {
      status: true,
      message: "Booking prescription deleted successfully",
      data: null,
    };
  }

  async createMultipleFiles(dto: CreateBookingPrescriptionDto) {
    const { booking_id, files } = dto;

    // Fetch the booking to get the user_id
    const booking = await this.bookingService.findById(booking_id);
    if (!booking) {
      throw new NotFoundException(`Booking with ID ${booking_id} not found`);
    }

    // Create multiple prescriptions
    const prescriptions = files.map((file) => {
      return this.bookingPrescriptionsRepository.create({
        booking_id,
        user_id: booking.user_id,
        file,
      });
    });

    // Save all prescriptions
    const savedPrescriptions =
      await this.bookingPrescriptionsRepository.save(prescriptions);
    booking.status = BookingStatus.COMPLETED;
    booking.completed_at = new Date();
    await this.bookingService.saveInstance(booking);
    return {
      status: true,
      message: "Files added successfully",
      data: savedPrescriptions,
    };
  }

  private async getUserIdFromBooking(booking_id: string): Promise<string> {
    // Simulate fetching user_id from the booking object
    const booking = await this.bookingPrescriptionsRepository.manager
      .getRepository("bookings")
      .findOne({ where: { id: booking_id } });

    if (!booking) {
      throw new Error(`Booking with ID ${booking_id} not found`);
    }

    return booking.user_id;
  }
}
