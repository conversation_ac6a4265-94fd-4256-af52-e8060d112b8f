import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  <PERSON>inColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import { Booking } from "src/bookings/entities/booking.entity";
import { User } from "src/users/entities/user.entity";

@Entity("booking_prescriptions")
export class BookingPrescription {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 36 })
  booking_id: string;

  @Column({ type: "varchar", length: 36 })
  user_id: string;

  @Column({ type: "varchar", length: 255 })
  file: string;

  @ManyToOne(() => Booking)
  @JoinColumn({ name: "booking_id" })
  booking: Booking;

  @ManyToOne(() => User)
  @JoinColumn({ name: "user_id" })
  user: User;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
