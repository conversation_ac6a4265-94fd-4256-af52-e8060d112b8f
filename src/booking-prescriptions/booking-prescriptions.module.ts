import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { BookingPrescriptionsService } from "./booking-prescriptions.service";
import { BookingPrescriptionsController } from "./booking-prescriptions.controller";
import { BookingPrescription } from "./entities/booking-prescription.entity";
import { BookingModule } from "src/bookings/bookings.module";
import { FileUploadModule } from "src/file-upload/file-upload.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([BookingPrescription]),
    BookingModule,
    FileUploadModule,
  ],
  controllers: [BookingPrescriptionsController],
  providers: [BookingPrescriptionsService],
})
export class BookingPrescriptionsModule {}
