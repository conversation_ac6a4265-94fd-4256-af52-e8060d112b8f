import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  BadGatewayException,
} from "@nestjs/common";
import { BookingPrescriptionsService } from "./booking-prescriptions.service";
import { CreateBookingPrescriptionDto } from "./dto/create-booking-prescription.dto";
import { UpdateBookingPrescriptionDto } from "./dto/update-booking-prescription.dto";
import { Paginate, PaginateQuery } from "nestjs-paginate";

@Controller("booking-prescriptions")
export class BookingPrescriptionsController {
  constructor(
    private readonly bookingPrescriptionsService: BookingPrescriptionsService,
  ) {}

  // @Post()
  // create(@Body() createBookingPrescriptionDto: CreateBookingPrescriptionDto) {
  //   return this.bookingPrescriptionsService.create(createBookingPrescriptionDto);
  // }

  @Post()
  async addMultipleFiles(@Body() dto: CreateBookingPrescriptionDto) {
    return this.bookingPrescriptionsService.createMultipleFiles(dto);
  }

  @Get()
  findAll(@Paginate() query: PaginateQuery, @Query() filters: any) {
    if (!filters.booking_id) {
      throw new BadGatewayException("Booking ID is required");
    }
    return this.bookingPrescriptionsService.findAll(query, filters.booking_id);
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.bookingPrescriptionsService.findOne(+id);
  }

  @Patch(":id")
  update(
    @Param("id") id: string,
    @Body() updateBookingPrescriptionDto: UpdateBookingPrescriptionDto,
  ) {
    return this.bookingPrescriptionsService.update(
      +id,
      updateBookingPrescriptionDto,
    );
  }

  @Delete(":id")
  async remove(@Param("id") id: string) {
    return this.bookingPrescriptionsService.remove(id);
  }
}
