import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { IsNull, Not, Repository } from "typeorm";
import { AppDetail } from "./entities/app-detail.entity";
import { CreateAppDetailDto } from "./dto/create-app-detail.dto";
import { UpdateAppDetailDto } from "./dto/update-app-detail.dto";

@Injectable()
export class AppDetailService {
  constructor(
    @InjectRepository(AppDetail)
    private readonly appDetailRepository: Repository<AppDetail>,
  ) {}

  async create(createAppDetailDto: CreateAppDetailDto): Promise<any> {
    const existsAppDetail = await this.appDetailRepository.findOne({
      where: { id: Not(IsNull()) },
    });
    if (existsAppDetail) {
      Object.assign(existsAppDetail, createAppDetailDto);
      await this.appDetailRepository.save(existsAppDetail);
    } else {
      const appDetail = this.appDetailRepository.create(createAppDetailDto);
      await this.appDetailRepository.save(appDetail);
    }
    return {
      status: true,
      message: "AppDetail created successfully",
    };
  }

  async findAll(): Promise<any> {
    const existsAppDetail = await this.appDetailRepository.findOne({
      where: { id: Not(IsNull()) },
    });
    return {
      status: true,
      message: "AppDetail fetched successfully",
      data: existsAppDetail ?? null,
    };
  }

  async findOne(id: string): Promise<AppDetail> {
    const appDetail = await this.appDetailRepository.findOne({ where: { id } });
    if (!appDetail) {
      throw new NotFoundException(`AppDetail with ID ${id} not found`);
    }
    return appDetail;
  }

  async update(
    id: string,
    updateAppDetailDto: UpdateAppDetailDto,
  ): Promise<AppDetail> {
    const appDetail = await this.findOne(id);
    Object.assign(appDetail, updateAppDetailDto);
    return this.appDetailRepository.save(appDetail);
  }

  async remove(id: string): Promise<void> {
    const appDetail = await this.findOne(id);
    await this.appDetailRepository.remove(appDetail);
  }
}
