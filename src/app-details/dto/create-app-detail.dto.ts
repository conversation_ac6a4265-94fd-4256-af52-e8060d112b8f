import {
  IsOptional,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ested,
  IsDate,
} from "class-validator";
import { Type } from "class-transformer";

class VersionDetailsDto {
  @IsString()
  min_version: string;

  @IsString()
  latest_version: string;

  @IsDate()
  @Type(() => Date) // Transform input to a Date object
  last_update_date: Date;
}

export class CreateAppDetailDto {
  @IsOptional()
  @IsString()
  app_store_link?: string;

  @IsOptional()
  @IsString()
  play_store_link?: string;

  @IsOptional()
  @IsNumber()
  platform_fee?: number;

  @IsOptional()
  @ValidateNested()
  @Type(() => VersionDetailsDto) // Use the custom validation class
  version_details_ios?: VersionDetailsDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => VersionDetailsDto) // Use the custom validation class
  version_details_android?: VersionDetailsDto;

  @IsOptional()
  @IsString()
  connect_link?: string;

  @IsOptional()
  @IsString()
  privacy_policy_link?: string;

  @IsOptional()
  @IsString()
  terms_and_conditions_link?: string;
}
