import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  UpdateDateColumn,
} from "typeorm";

@Entity("app_details")
export class AppDetail {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  app_store_link: string | null;

  @Column({ type: "varchar", length: 255, nullable: true })
  play_store_link: string | null;

  @Column({ type: "decimal", precision: 8, scale: 2, nullable: true })
  platform_fee: number | null;

  @Column({ type: "simple-json", nullable: true })
  version_details_ios: {
    min_version: string;
    latest_version: string;
    last_update_date: Date;
  };

  @Column({ type: "simple-json", nullable: true })
  version_details_android: {
    min_version: string;
    latest_version: string;
    last_update_date: Date;
  };

  @Column({ type: "varchar", length: 255, nullable: true })
  connect_link: string | null;

  @Column({ type: "varchar", length: 255, nullable: true })
  terms_and_conditions_link: string | null;

  @Column({ type: "varchar", length: 255, nullable: true })
  privacy_policy_link: string | null;
}
