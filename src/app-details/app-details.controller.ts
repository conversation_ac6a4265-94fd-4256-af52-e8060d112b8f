import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from "@nestjs/common";
import { AppDetailService } from "./app-details.service";
import { CreateAppDetailDto } from "./dto/create-app-detail.dto";
import { UpdateAppDetailDto } from "./dto/update-app-detail.dto";

@Controller("app-details")
export class AppDetailsController {
  constructor(private readonly appDetailsService: AppDetailService) {}

  @Post()
  async create(@Body() createAppDetailsDto: CreateAppDetailDto) {
    return this.appDetailsService.create(createAppDetailsDto);
  }

  @Get()
  async findAll() {
    return this.appDetailsService.findAll();
  }

  @Get(":id")
  async findOne(@Param("id") id: string) {
    return this.appDetailsService.findOne(id);
  }

  @Patch(":id")
  async update(
    @Param("id") id: string,
    @Body() updateAppDetailsDto: UpdateAppDetailDto,
  ) {
    return this.appDetailsService.update(id, updateAppDetailsDto);
  }

  @Delete(":id")
  async remove(@Param("id") id: string) {
    return this.appDetailsService.remove(id);
  }
}
