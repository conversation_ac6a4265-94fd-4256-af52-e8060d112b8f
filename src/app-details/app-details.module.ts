import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AppDetailService } from "./app-details.service";
import { AppDetailsController } from "./app-details.controller";
import { AppDetail } from "./entities/app-detail.entity";

@Module({
  imports: [TypeOrmModule.forFeature([AppDetail])],
  controllers: [AppDetailsController],
  providers: [AppDetailService],
})
export class AppDetailsModule {}
