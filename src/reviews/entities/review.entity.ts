import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  Jo<PERSON><PERSON><PERSON>um<PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import { User } from "src/users/entities/user.entity";
import { Doctor } from "src/doctors/entities/doctor.entity";
import { Hospital } from "src/hospitals/entities/hospital.entity";

@Entity("reviews")
export class Review {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "uuid" })
  user_id: string;

  @ManyToOne(() => User, (user) => user.reviews, { onDelete: "CASCADE" })
  @JoinColumn({ name: "user_id" })
  user: User;

  @Column({ type: "uuid", nullable: true })
  doctor_id: string;

  @ManyToOne(() => Doctor, (doctor) => doctor.reviews, { onDelete: "CASCADE" })
  @JoinColumn({ name: "doctor_id" })
  doctor: Doctor;

  @Column({ type: "uuid", nullable: true })
  hospital_id: string;

  @ManyToOne(() => Hospital, (hospital) => hospital.reviews, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "hospital_id" })
  hospital: Hospital;

  @Column({ type: "decimal", precision: 2, scale: 1 })
  rating: number;

  @Column({ type: "text", nullable: true })
  review_note: string;

  @CreateDateColumn({ name: "created_at" })
  created_at: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updated_at: Date;
}
