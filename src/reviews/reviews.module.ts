import { Module, forwardRef } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { ReviewsService } from "./reviews.service";
import { ReviewsController } from "./reviews.controller";
import { Review } from "./entities/review.entity";
import { CommonModule } from "src/common/common.module";
import { HospitalsModule } from "src/hospitals/hospitals.module";
import { DoctorsModule } from "src/doctors/doctors.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([Review]),
    CommonModule,
    forwardRef(() => HospitalsModule), // Use forwardRef to resolve circular dependency
    forwardRef(() => DoctorsModule), // Use forwardRef to resolve circular dependency
  ],
  controllers: [ReviewsController],
  providers: [ReviewsService],
  exports: [ReviewsService],
})
export class ReviewsModule {}
