import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, Not, IsNull } from "typeorm";
import { Review } from "./entities/review.entity";
import { CreateReviewDto } from "./dto/create-review.dto copy";
import { UpdateReviewDto } from "./dto/update-review.dto";
import { PaginateQuery, paginate } from "nestjs-paginate";
import { HospitalsService } from "src/hospitals/hospitals.service";
import { DoctorsService } from "src/doctors/doctors.service";

@Injectable()
export class ReviewsService {
  constructor(
    @InjectRepository(Review)
    private readonly reviewRepository: Repository<Review>,
    private readonly hospitalsService: HospitalsService,
    private readonly doctorsService: DoctorsService,
  ) {}

  async create(createReviewDto: CreateReviewDto): Promise<any> {
    const existingReview = await this.reviewRepository.findOne({
      where: {
        user_id: createReviewDto.user_id,
        doctor_id: createReviewDto.doctor_id,
      },
    });
    if (existingReview) {
      return {
        status: false,
        message: "You have already reviewed this doctor or hospital",
        data: existingReview,
      };
    }
    const review = this.reviewRepository.create(createReviewDto);
    const savedReview = await this.reviewRepository.save(review);
    if (createReviewDto.hospital_id) {
      await this.hospitalsService.calculateRatings(createReviewDto.hospital_id);
    }
    if (createReviewDto.doctor_id) {
      await this.doctorsService.calculateRatings(createReviewDto.doctor_id);
    }
    return {
      status: true,
      message: "Review created successfully",
      data: savedReview,
    };
  }

  async findAll(query: PaginateQuery, filters: any) {
    let where: any = {
      id: Not(IsNull()),
    };

    // Filter by user_id
    if (filters.user_id) {
      where.user_id = filters.user_id;
    }

    // Filter by doctor_id
    if (filters.doctor_id) {
      where.doctor_id = filters.doctor_id;
    }

    // Filter by hospital_id
    if (filters.hospital_id) {
      where.hospital_id = filters.hospital_id;
    }

    // Filter by rating
    if (filters.rating) {
      where.rating = filters.rating;
    }

    return paginate(query, this.reviewRepository, {
      sortableColumns: ["id", "created_at", "rating"],
      where,
      relations: ["user", "doctor", "hospital"], // Include related entities if needed
    });
  }

  async findAllUser(query: PaginateQuery, filters: any) {
    let where: any = {
      id: Not(IsNull()),
    };

    // Filter by user_id
    if (filters.user_id) {
      where.user_id = filters.user_id;
    }

    // Filter by doctor_id
    if (filters.doctor_id) {
      where.doctor_id = filters.doctor_id;
    }

    // Filter by hospital_id
    if (filters.hospital_id) {
      where.hospital_id = filters.hospital_id;
    }

    // Filter by rating
    if (filters.rating) {
      where.rating = filters.rating;
    }

    return paginate(query, this.reviewRepository, {
      sortableColumns: ["id", "created_at", "rating"],
      where,
      relations: ["user", "doctor", "hospital"], // Include related entities if needed
    });
  }

  async findOne(id: string): Promise<Review> {
    const review = await this.reviewRepository.findOne({ where: { id } });
    if (!review) {
      throw new NotFoundException(`Review with ID ${id} not found`);
    }
    return review;
  }

  async update(id: string, updateReviewDto: UpdateReviewDto): Promise<any> {
    const review = await this.findOne(id);
    Object.assign(review, updateReviewDto);
    await this.reviewRepository.save(review);
    if (review.hospital_id) {
      await this.hospitalsService.calculateRatings(review.hospital_id);
    }
    if (review.doctor_id) {
      await this.doctorsService.calculateRatings(review.doctor_id);
    }
    return {
      status: true,
      message: "Review updated successfully",
    };
  }

  async remove(id: string): Promise<any> {
    const review = await this.findOne(id);
    await this.reviewRepository.remove(review);

    if (review.hospital_id) {
      await this.hospitalsService.calculateRatings(review.hospital_id);
    }
    if (review.doctor_id) {
      await this.doctorsService.calculateRatings(review.doctor_id);
    }
    return {
      status: true,
      message: "Review removed successfully",
    };
  }
}
