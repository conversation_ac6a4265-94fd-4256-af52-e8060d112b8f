import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Req,
  UseGuards,
} from "@nestjs/common";
import { ReviewsService } from "./reviews.service";
import { CreateReviewDto } from "./dto/create-review.dto copy";
import { UpdateReviewDto } from "./dto/update-review.dto";
import { Paginate, PaginateQuery } from "nestjs-paginate";
import { Request } from "express";
import { AccessTokenGuard } from "src/common/guards/accessToken.guard";

@Controller("reviews")
export class ReviewsController {
  constructor(private readonly reviewsService: ReviewsService) {}

  @UseGuards(AccessTokenGuard)
  @Post()
  create(@Body() createReviewDto: CreateReviewDto) {
    return this.reviewsService.create(createReviewDto);
  }

  @Get()
  findAll(
    @Paginate() query: PaginateQuery,
    @Query() filters: any,
    @Req() req: Request,
  ) {
    return this.reviewsService.findAll(query, filters);
  }

  @Get("users")
  findAllUser(
    @Paginate() query: PaginateQuery,
    @Query() filters: any,
    @Req() req: Request,
  ) {
    return this.reviewsService.findAllUser(query, filters);
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.reviewsService.findOne(id);
  }

  @UseGuards(AccessTokenGuard)
  @Patch(":id")
  update(@Param("id") id: string, @Body() updateReviewDto: UpdateReviewDto) {
    return this.reviewsService.update(id, updateReviewDto);
  }

  @UseGuards(AccessTokenGuard)
  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.reviewsService.remove(id);
  }
}
