import {
  IsNotE<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from "class-validator";

export class CreateReviewDto {
  @IsUUID()
  @IsNotEmpty()
  user_id: string;

  @IsUUID()
  @IsOptional()
  doctor_id?: string;

  @IsUUID()
  @IsOptional()
  hospital_id?: string;

  @IsNumber()
  @Min(0)
  @Max(5)
  @IsNotEmpty()
  rating: number;

  @IsOptional()
  review_note?: string;
}
