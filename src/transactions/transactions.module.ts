import { forwardRef, <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { TransactionsService } from "./transactions.service";
import { TransactionsController } from "./transactions.controller";
import { Transaction } from "./entities/transaction.entity";
import { BookingModule } from "src/bookings/bookings.module";
import { CashfreeModule } from "src/cashfree/cashfree.module";
import { CouponsModule } from "src/coupons/coupons.module";
import { TimeSlotModule } from "src/time-slots/time-slots.module";
import { NotificationsModule } from "src/notifications/notifications.module";
import { CommonModule } from "src/common/common.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([Transaction]),
    forwardRef(() => BookingModule),
    CashfreeModule,
    CouponsModule,
    TimeSlotModule,
    NotificationsModule,
    CommonModule,
  ],
  controllers: [TransactionsController],
  providers: [TransactionsService],
  exports: [TransactionsService],
})
export class TransactionsModule {}
