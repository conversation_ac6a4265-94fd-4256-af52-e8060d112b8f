import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Res,
  BadRequestException,
  UseGuards,
  Req,
  Query,
} from "@nestjs/common";
import { Paginate, PaginateQuery } from "nestjs-paginate";
import { AccessTokenGuard } from "src/common/guards/accessToken.guard";
import { TransactionsService } from "./transactions.service";
import { CreateTransactionDto } from "./dto/create-transaction.dto";
import { UpdateTransactionDto } from "./dto/update-transaction.dto";
import { Response } from "express";

@Controller("transactions")
export class TransactionsController {
  constructor(private readonly transactionsService: TransactionsService) {}

  @UseGuards(AccessTokenGuard)
  @Get("doctors/:doctor_id")
  async listDoctorTransactions(
    @Paginate() query: PaginateQuery,
    @Param("doctor_id") doctor_id: string,
  ) {
    return this.transactionsService.listDoctorTransactions(query, doctor_id);
  }

  // @Get('export')
  // async exportTransactions(
  //   @Param('doctor_id') doctor_id: string,
  //   @Query() filters: any,
  //   @Res() response: Response,
  // ) {
  //   const { buffer, filename } = await this.transactionsService.exportTransactions( filters);
  //   response.setHeader(
  //     'Content-Type',
  //     'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //   );
  //   response.setHeader(
  //     'Content-Disposition',
  //     `attachment; filename=${filename}`,
  //   );
  //   response.send(buffer);
  // }

  @UseGuards(AccessTokenGuard)
  @Get("hospitals/:hospital_id")
  async listHospitalTransactions(
    @Paginate() query: PaginateQuery,
    @Param("hospital_id") hospital_id: string,
  ) {
    return this.transactionsService.listHospitalTransactions(
      query,
      hospital_id,
    );
  }

  // @UseGuards(AccessTokenGuard)
  @Get("hospitals/:hospital_id/export")
  async exportHospitalTransactions(
    @Param("hospital_id") hospital_id: string,
    @Query() filters: any,
    @Res() response: Response,
  ) {
    const { buffer, filename } =
      await this.transactionsService.exportTransactions(hospital_id, filters);
    response.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    );
    response.setHeader(
      "Content-Disposition",
      `attachment; filename=${filename}`,
    );
    response.send(buffer);
  }

  @Get("users/:user_id")
  async listUserTransactions(
    @Paginate() query: PaginateQuery,
    @Param("user_id") user_id: string,
  ) {
    return this.transactionsService.listUserTransactions(query, user_id);
  }

  @UseGuards(AccessTokenGuard)
  @Get(":id/verify-order")
  async verifyOrder(@Param("id") id: string, @Res() res: Response) {
    let result = await this.transactionsService.verifyOrder(id, res);
    return {
      statusCode: 200,
      message: "Order verified successfully",
      // data: result,
    };
  }

  @UseGuards(AccessTokenGuard)
  @Post(":id/verify-payment")
  async verifyTransaction(@Param("id") id: string, @Body() res: any) {
    if (!res.order_id) {
      throw new BadRequestException("order_id is required");
    }
    let result = await this.transactionsService.verifyTransaction(id, res);
    return {
      statusCode: 200,
      message: "Order verified successfully",
      // data: result,
    };
  }
}
