import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  Join<PERSON><PERSON>um<PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import { User } from "src/users/entities/user.entity";
import { Doctor } from "src/doctors/entities/doctor.entity";
import { Hospital } from "src/hospitals/entities/hospital.entity";
import { Booking } from "src/bookings/entities/booking.entity";
import { TransactionStatus, BookingType } from "src/common/enums/status.enum";
import { Coupon } from "src/coupons/entities/coupon.entity";

@Entity("transactions")
export class Transaction {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "uuid" })
  user_id: string;

  @ManyToOne(() => User, (user) => user.transactions, { onDelete: "CASCADE" })
  @JoinColumn({ name: "user_id" })
  user: User;

  @Column({ type: "uuid", nullable: true })
  doctor_id: string;

  @ManyToOne(() => Doctor, (doctor) => doctor.transactions, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "doctor_id" })
  doctor: Doctor;

  @Column({ type: "uuid", nullable: true })
  hospital_id: string;

  @ManyToOne(() => Hospital, (hospital) => hospital.transactions, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "hospital_id" })
  hospital: Hospital;

  @Column({ type: "uuid", nullable: true })
  booking_id: string;

  @Column({ type: "boolean", nullable: true })
  wallet_updated: boolean;

  @ManyToOne(() => Booking, (booking) => booking.transactions, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "booking_id" })
  booking: Booking;

  @Column({ type: "uuid", nullable: true })
  hospital_service_id: string;

  @Column({ type: "boolean", default: false })
  is_settled: boolean;

  @Column({ type: "datetime" })
  paid_date: Date;

  @Column({ type: "decimal", precision: 10, scale: 2, nullable: true })
  amount: number;

  @Column({ type: "varchar", length: 36, nullable: true })
  coupon_id: string | null;

  @ManyToOne(() => Coupon, { nullable: true, onDelete: "SET NULL" })
  @JoinColumn({ name: "coupon_id" })
  coupon: Coupon;

  @Column({
    type: "enum",
    enum: TransactionStatus,
    default: TransactionStatus.STARTED,
  })
  status: TransactionStatus;

  @Column({ type: "varchar", length: 255, nullable: true })
  note: string;

  @Column({
    type: "enum",
    enum: BookingType,
    default: BookingType.CONSULTATION,
  })
  type: BookingType;

  @Column({ type: "varchar", length: 255, nullable: true })
  order_id?: string;

  @Column({ type: "boolean", default: true, nullable: true })
  is_online: boolean | null;

  @Column({ type: "varchar", length: 255, nullable: true })
  payment_type: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
