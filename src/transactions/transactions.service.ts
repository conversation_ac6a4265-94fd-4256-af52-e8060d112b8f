import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { In, Repository, Between } from "typeorm";
import { Transaction } from "./entities/transaction.entity";
import { CreateTransactionDto } from "./dto/create-transaction.dto";
import { BookingService } from "src/bookings/bookings.service";
import { Booking, BookingStatus } from "src/bookings/entities/booking.entity";
import { CashfreeService } from "src/cashfree/cashfree.service";
import { BookingType, TransactionStatus } from "src/common/enums/status.enum";
import { CouponsService } from "src/coupons/coupons.service";
import { TimeSlotService } from "src/time-slots/time-slots.service";
import { NotificationsService } from "src/notifications/notifications.service";
import {
  CreateNotificationDto,
  NotificationType,
} from "src/notifications/dto/create-notification.dto";
import { PaginateQuery, paginate } from "nestjs-paginate";
import * as ExcelJS from "exceljs";

@Injectable()
export class TransactionsService {
  constructor(
    @InjectRepository(Transaction)
    private readonly transactionRepository: Repository<Transaction>,
    @Inject(forwardRef(() => BookingService))
    private readonly bookingService: BookingService,

    private readonly cashfreeService: CashfreeService,
    private readonly couponsService: CouponsService,
    private readonly timeSlotService: TimeSlotService,
    private readonly notificationService: NotificationsService,
  ) {}

  // Function to create a new transaction
  async createTransaction(
    createTransactionDto: CreateTransactionDto,
  ): Promise<Transaction> {
    const transaction = this.transactionRepository.create(createTransactionDto);
    return await this.saveInstance(transaction);
  }

  // Function to save a transaction instance to the database
  async saveInstance(transaction: Transaction): Promise<Transaction> {
    return await this.transactionRepository.save(transaction);
  }

  // Function to initiate a transaction instance with default values
  async initiateInstance(): Promise<Transaction> {
    return new Transaction();
  }

  async verifyOrder(id: string, res) {
    const transaction = await this.transactionRepository.findOne({
      where: {
        id,
      },
    });

    if (!transaction || transaction.status == TransactionStatus.COMPLETED) {
      throw new NotFoundException("Transaction not found");
    }
    const order_id = transaction.order_id;

    const response = await this.cashfreeService.verifyOrder(order_id);
    // let redirectUrl = '';

    if (response.order_status == "PAID") {
      transaction.status = TransactionStatus.COMPLETED;
      transaction.paid_date = new Date();
      await this.transactionRepository.save(transaction);
      const booking = await this.bookingService.findById(
        transaction.booking_id,
      );
      if (booking.type == BookingType.FAST_TAG) {
        booking.status = BookingStatus.ACCEPTED;
      } else if (booking.hospital_id) {
        booking.status = BookingStatus.PENDING;
      } else {
        booking.status = BookingStatus.ACCEPTED;
      }
      if (booking.timeSlot) {
        await this.timeSlotService.markSlotAsBooked(
          booking.doctor_id,
          booking.appointmentDate,
          booking.timeSlot,
        );
      }
      await this.bookingService.saveInstance(booking);
      await this.sendNotificationSuccess(booking);
      if (transaction.coupon_id) {
        await this.couponsService.updateCouponUsageCount(
          transaction.coupon_id,
          transaction.user_id,
        );
      }
    }
    return {
      status: "success",
      message: "Transaction completed successfully",
    };
  }

  async verifyTransaction(id: string, body: any) {
    const { order_id } = body;
    const transaction = await this.transactionRepository.findOne({
      where: {
        id,
      },
    });

    if (!transaction || transaction.status == TransactionStatus.COMPLETED) {
      throw new NotFoundException("Transaction not found or already completed");
    }
    const saved_order_id = transaction.order_id;
    if (order_id != saved_order_id) {
      throw new NotFoundException("Order ID does not match");
    }

    const response = await this.cashfreeService.verifyOrder(saved_order_id);
    // let redirectUrl = '';

    if (response.order_status == "PAID") {
      transaction.status = TransactionStatus.COMPLETED;
      transaction.paid_date = new Date();
      await this.transactionRepository.save(transaction);
      const booking = await this.bookingService.findById(
        transaction.booking_id,
      );
      if (booking.type == BookingType.FAST_TAG) {
        booking.status = BookingStatus.ACCEPTED;
      } else if (booking.hospital_id) {
        booking.status = BookingStatus.PENDING;
      } else {
        booking.status = BookingStatus.ACCEPTED;
      }
      if (booking.timeSlot) {
        await this.timeSlotService.markSlotAsBooked(
          booking.doctor_id,
          booking.appointmentDate,
          booking.timeSlot,
        );
      }
      await this.bookingService.saveInstance(booking);
      await this.sendNotificationSuccess(booking);
      if (transaction.coupon_id) {
        await this.couponsService.updateCouponUsageCount(
          transaction.coupon_id,
          transaction.user_id,
        );
      }
      if (booking.doctor_id) {
        await this.bookingService.updateTokenNumber(
          booking.doctor_id,
          booking.appointmentDate,
          BookingType.CONSULTATION,
        );
      } else if (booking.hospital_service_id) {
        await this.bookingService.updateTokenNumber(
          booking.hospital_service_id,
          booking.appointmentDate,
          BookingType.SERVICE,
        );
      }
    }

    return {
      status: "success",
      message: "Transaction completed successfully",
    };
  }

  async sendNotificationSuccess(booking: Booking) {
    const notification: CreateNotificationDto = {
      user_ids: [booking.user_id],
      title: "Transaction Successful",
      body: `Your transaction was successful for booking ID: ${booking.booking_id}.`,
      type: [NotificationType.IN_APP, NotificationType.PUSH],
    };

    await this.notificationService.send(notification);
  }

  async listDoctorTransactions(query: PaginateQuery, doctor_id: string) {
    return paginate(query, this.transactionRepository, {
      sortableColumns: ["created_at", "amount", "id"],
      where: { doctor_id, status: TransactionStatus.COMPLETED },
      defaultSortBy: [["created_at", "DESC"]],
      relations: ["user"],
    });
  }

  async listHospitalTransactions(query: PaginateQuery, hospital_id: string) {
    return paginate(query, this.transactionRepository, {
      sortableColumns: ["created_at", "amount", "id"],
      where: { hospital_id, status: TransactionStatus.COMPLETED },
      defaultSortBy: [["created_at", "DESC"]],
      relations: ["user"],
    });
  }

  async listUserTransactions(query: PaginateQuery, user_id: string) {
    return paginate(query, this.transactionRepository, {
      sortableColumns: ["created_at", "amount", "id"],
      where: { user_id, status: TransactionStatus.COMPLETED },
      defaultSortBy: [["created_at", "DESC"]],
    });
  }

  async findTransactionsForWalletUpdateHospital(hospital_id: string) {
    return await this.transactionRepository.find({
      where: {
        hospital_id,
        wallet_updated: false,
        status: TransactionStatus.COMPLETED,
        is_online: true,
      },
    });
  }

  async findTransactionsForWalletUpdateDoctor(doctor_id: string) {
    return await this.transactionRepository.find({
      where: {
        doctor_id,
        wallet_updated: false,
        status: TransactionStatus.COMPLETED,
        is_online: true,
      },
    });
  }

  async updateTransactionsAsWalletUpdated(ids: string[]) {
    await this.transactionRepository.update(
      { id: In(ids) },
      {
        wallet_updated: true,
      },
    );
  }

  private async createExcelWorkbook(data: any[], type: "hospital" | "doctor") {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Transactions");

    // Define columns based on transaction type
    const commonColumns = [
      { header: "Transaction ID", key: "id", width: 20 },
      { header: "Booking ID", key: "booking_id", width: 20 },
      { header: "User Name", key: "user_name", width: 20 },
      { header: "Amount", key: "amount", width: 12 },
      { header: "Status", key: "status", width: 15 },
      { header: "Order ID", key: "order_id", width: 20 },
      { header: "Paid Date", key: "paid_date", width: 20 },
      { header: "Created At", key: "created_at", width: 20 },
    ];
    const specificColumns =
      type === "hospital"
        ? [{ header: "Doctor Name", key: "doctor_name", width: 20 }]
        : [{ header: "Hospital Name", key: "hospital_name", width: 20 }];
    worksheet.columns = [...commonColumns, ...specificColumns];

    data.forEach((row) => {
      worksheet.addRow(row);
    });
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE0E0E0" },
    };
    worksheet.getColumn("amount").numFmt = "₹#,##0.00";
    return workbook;
  }

  async exportTransactions(hospital_id: string, filters: any) {
    const where: any = { hospital_id, status: "completed", ...filters };

    if (filters.from_date && filters.to_date) {
      const fromDate = new Date(filters.from_date);
      fromDate.setHours(0, 0, 0, 0);
      const toDate = new Date(filters.to_date);
      toDate.setHours(23, 59, 59, 999);
      where.paid_date = Between(fromDate, toDate);
      delete where.from_date;
      delete where.to_date;
    }

    const transactions = await this.transactionRepository.find({
      where,
      relations: ["user", "doctor"],
      order: { created_at: "DESC" },
    });
    const data = transactions.map((tx) => ({
      id: tx.id,
      booking_id: tx.booking_id,
      user_name: tx.user?.first_name || "N/A",
      amount: tx.amount,
      status: tx.status,
      order_id: tx.order_id,
      paid_date: tx.paid_date ? tx.paid_date.toISOString() : "N/A",
      created_at: tx.created_at ? tx.created_at.toISOString() : "N/A",
      doctor_name: tx.doctor?.name || "N/A",
    }));
    const workbook = await this.createExcelWorkbook(data, "hospital");
    const buffer = await workbook.xlsx.writeBuffer();
    return {
      buffer,
      filename: `hospital_transactions_${hospital_id}.xlsx`,
    };
  }
}
