import { <PERSON>du<PERSON> } from "@nestjs/common";
import { ChildUsersService } from "./child_users.service";
import { ChildUsersController } from "./child_users.controller";
import { UsersModule } from "src/users/users.module";
import { TypeOrmModule } from "@nestjs/typeorm";
import { ChildUser } from "./entities/child_user.entity";

@Module({
  imports: [TypeOrmModule.forFeature([ChildUser]), UsersModule],
  controllers: [ChildUsersController],
  providers: [ChildUsersService],
})
export class ChildUsersModule {}
