import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { CreateChildUserDto } from "./dto/create-child_user.dto";
import { UpdateChildUserDto } from "./dto/update-child_user.dto";
import { InjectRepository } from "@nestjs/typeorm";
import { ChildUser } from "./entities/child_user.entity";
import { Repository } from "typeorm";
import { recordExists } from "src/utils/record-exists";
import { UsersService } from "src/users/users.service";
import { paginate, PaginateQuery } from "nestjs-paginate";

@Injectable()
export class ChildUsersService {
  constructor(
    @InjectRepository(ChildUser)
    private childUserRepository: Repository<ChildUser>,
    private readonly userService: UsersService,
  ) {}
  async create(
    createChildUserDto: CreateChildUserDto,
    user_id: string,
  ): Promise<any> {
    const userExists = await this.userService.findOneByParam({
      id: user_id,
    });

    if (!userExists) {
      throw new BadRequestException(`User with ID ${user_id} doesnot exist.`);
    }

    if (userExists.user_type !== "user") {
      throw new BadRequestException(`Only clients can create family members`);
    }

    const childUser = this.childUserRepository.create({
      ...createChildUserDto,
    });
    childUser.user_id = user_id;
    const newchildUser = await this.childUserRepository.save(childUser);
    return {
      status: "success",
      message: "Child user created successfully",
      data: newchildUser,
    };
  }

  async findAllByUserId(query: PaginateQuery, userID: string): Promise<any> {
    return paginate(query, this.childUserRepository, {
      sortableColumns: ["fullname"],
      where: {
        user_id: userID,
      },
    });
  }

  async findOne(id: string): Promise<any> {
    const childUser = await this.childUserRepository.findOne({
      where: { id },
      relations: ["user"],
    });

    if (!childUser) {
      throw new NotFoundException(`Child user with ID ${id} not found`);
    }

    return {
      status: true,
      message: "Child user found successfully",
      data: childUser,
    };
  }

  async findOneByParam(param: any): Promise<any> {
    const childUser = await this.childUserRepository.findOne({
      where: param,
      relations: ["user"],
    });

    if (!childUser) {
      throw new NotFoundException(`Child user not found`);
    }

    return childUser;
  }
  async update(
    id: string,
    updateChildUserDto: UpdateChildUserDto,
  ): Promise<any> {
    const childUser = await this.findOneByParam({ id });

    Object.assign(childUser, {
      ...updateChildUserDto,
      appointment_details: updateChildUserDto.appointment_details
        ? JSON.stringify(updateChildUserDto.appointment_details)
        : childUser.appointment_details,
    });
    await this.childUserRepository.save(childUser);
    return {
      status: true,
      message: "Child user updated successfully",
      data: childUser,
    };
  }

  async remove(id: string): Promise<any> {
    const result = await this.childUserRepository.delete(id);

    if (result.affected === 0) {
      throw new NotFoundException(`Child user with ID ${id} not found`);
    }
    return {
      status: true,
      message: "Child user deleted successfully",
    };
  }
}
