import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Req,
  BadRequestException,
} from "@nestjs/common";
import { ChildUsersService } from "./child_users.service";
import { CreateChildUserDto } from "./dto/create-child_user.dto";
import { UpdateChildUserDto } from "./dto/update-child_user.dto";
import { AccessTokenGuard } from "src/common/guards/accessToken.guard";
import { Paginate, PaginateQuery } from "nestjs-paginate";

@Controller("child-users")
@UseGuards(AccessTokenGuard)
export class ChildUsersController {
  constructor(private readonly childUsersService: ChildUsersService) {}

  @Post()
  create(@Body() createChildUserDto: CreateChildUserDto, @Req() req: Request) {
    return this.childUsersService.create(createChildUserDto, req["uid"]);
  }

  @Get()
  findAll(@Paginate() query: PaginateQuery, @Query("user_id") userID: string) {
    if (!userID) {
      throw new BadRequestException("user_id is required");
    }
    return this.childUsersService.findAllByUserId(query, userID);
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.childUsersService.findOne(id);
  }

  @Patch(":id")
  update(
    @Param("id") id: string,
    @Body() updateChildUserDto: UpdateChildUserDto,
  ) {
    return this.childUsersService.update(id, updateChildUserDto);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.childUsersService.remove(id);
  }
}
