import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  Column,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "typeorm";
import { User } from "../../users/entities/user.entity";

@Entity("child_users")
export class ChildUser {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column()
  fullname: string;

  @Column({ nullable: true })
  profile_picture: string | null;

  @Column()
  gender: string;

  @Column()
  age: number;

  @Column()
  relation?: string;

  @Column("json")
  appointment_details: Record<string, any>;

  @Column("simple-array")
  files: string[];

  @Column({ type: "uuid" })
  user_id: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: "user_id" })
  user: User;
}
