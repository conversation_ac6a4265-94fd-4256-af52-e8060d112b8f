import {
  <PERSON>String,
  <PERSON><PERSON>umber,
  IsArray,
  IsUUID,
  IsObject,
  IsNotEmpty,
  IsOptional,
} from "class-validator";

export class CreateChildUserDto {
  @IsString()
  @IsNotEmpty()
  fullname: string;

  @IsString()
  @IsNotEmpty()
  gender: string;

  @IsString()
  relation: string;

  @IsNumber()
  @IsNotEmpty()
  age: number;

  @IsObject()
  @IsOptional()
  appointment_details: Record<string, any>;

  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  files: string[];

  @IsOptional()
  profile_picture: string | null;
}
