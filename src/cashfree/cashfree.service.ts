import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { Cashfree } from "cashfree-pg";
import { Transaction } from "src/transactions/entities/transaction.entity";
import { User } from "src/users/entities/user.entity";

@Injectable()
export class CashfreeService {
  private apiURL = process.env.API_URL || "https://api.zodoai.com";
  constructor() {
    Cashfree.XClientId =
      process.env.CASHFREE_API_ID || "TEST10460097025258a4e8fa6dfff59679006401";
    Cashfree.XClientSecret =
      process.env.CASHFREE_API_SECRET ||
      "cfsk_ma_test_73b092ab85564105d080b98e3b2e631a_a3d51f0b";
    Cashfree.XEnvironment =
      process.env.CASHFREE_ENVIRONMENT == "production"
        ? Cashfree.Environment.PRODUCTION
        : Cashfree.Environment.SANDBOX;
  }
  async createOrder(user: User, transaction: Transaction) {
    const request = {
      order_amount: transaction.amount,
      order_currency: "INR",
      customer_details: {
        customer_email: user.email,
        customer_name: user.phone,
        customer_phone: await this.validatePhoneNumber(user.phone),
        customer_id: user.id,
      },
      order_meta: {
        return_url: `${this.apiURL}/api/transactions/${transaction.id}/verify-order`,
      },
      order_note: "",
    };

    try {
      const response = await Cashfree.PGCreateOrder("2023-08-01", request);
      // console.log(response)
      return response.data; // Return the response data to the controller or caller
    } catch (error) {
      // Handle the error appropriately
      throw new HttpException(
        error.response?.data || "Error setting up order request",
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async validatePhoneNumber(phone: string) {
    return phone.replace(/\s+/g, ""); // Remove spaces
  }

  async verifyOrder(order_id: string) {
    return await Cashfree.PGFetchOrder("2022-09-01", order_id)
      .then((response) => {
        // console.log('Order fetched successfully:', response.data);
        return response.data; // Ensure data is returned after logging
      })
      .catch((error) => {
        console.error(
          "Error fetching order:",
          error.response?.data?.message || error.message,
        );
        throw error; // Propagate the error to the caller
      });
  }
}
