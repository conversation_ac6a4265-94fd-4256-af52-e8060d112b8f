import { Controller, Get, Param, Query } from "@nestjs/common";
import { DashboardService } from "./dashboard.service";

@Controller("dashboard")
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get("counts")
  async getCounts() {
    return this.dashboardService.getCounts();
  }

  @Get("bookings-by-month")
  async getBookingCountsByMonth(@Query() filter: any) {
    return this.dashboardService.getBookingCountsByMonth(filter);
  }

  @Get("hospitals/:id")
  async getHospitalAnalytics(@Param("id") hospital_id: string) {
    return this.dashboardService.getHospitalAnalytics(hospital_id);
  }
  @Get("doctors/:id")
  async getDoctorAnalytics(@Param("id") doctor_id: string) {
    return this.dashboardService.getDoctorAnalytics(doctor_id);
  }
}
