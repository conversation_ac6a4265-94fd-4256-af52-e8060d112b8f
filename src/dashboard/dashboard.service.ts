import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { In, IsNull, Not, Repository } from "typeorm";
import { Doctor } from "src/doctors/entities/doctor.entity";
import { Hospital } from "src/hospitals/entities/hospital.entity";
import { Booking } from "src/bookings/entities/booking.entity";
import { BookingStatus } from "src/bookings/entities/booking.entity";
import { BookingType } from "src/common/enums/status.enum";
import {
  Settlement,
  SettlementStatus,
} from "src/settlements/entities/settlement.entity";
import * as moment from "moment";
import { User } from "src/users/entities/user.entity";

@Injectable()
export class DashboardService {
  constructor(
    @InjectRepository(Doctor)
    private readonly doctorRepository: Repository<Doctor>,
    @InjectRepository(Hospital)
    private readonly hospitalRepository: Repository<Hospital>,
    @InjectRepository(Booking)
    private readonly bookingRepository: Repository<Booking>,
    @InjectRepository(Settlement)
    private readonly settlementRepository: Repository<Settlement>,

    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async getCounts(): Promise<any> {
    // Count active doctors with hospital_id as null
    const activeDoctorsCount = await this.doctorRepository.count({
      where: { status: "active", hospital_id: null },
    });

    // Count active hospitals
    const activeHospitalsCount = await this.hospitalRepository.count({
      where: { status: "active" },
    });

    // Count bookings of type FAST_TAG with status ACCEPTED
    const fastTagBookingsCount = await this.bookingRepository.count({
      where: { type: BookingType.FAST_TAG, status: BookingStatus.ACCEPTED },
    });

    // Count settlements requested for hospitals
    const settlementCountHospital = await this.settlementRepository.count({
      where: { status: SettlementStatus.REQUESTED, hospital_id: Not(IsNull()) },
    });

    // Count settlements requested for doctors
    const settlementCountDoctor = await this.settlementRepository.count({
      where: { status: SettlementStatus.REQUESTED, doctor_id: Not(IsNull()) },
    });

    const settlementCountApproved = await this.settlementRepository.count({
      where: { status: SettlementStatus.APPROVED },
    });

    // Calculate revenue for FAST_TAG bookings with status ACCEPTED
    const fastTagBookingsRevenue = await this.bookingRepository
      .createQueryBuilder("booking")
      .select("SUM(booking.amount)", "total")
      .where("booking.type = :type", { type: BookingType.FAST_TAG })
      .andWhere("booking.status = :status", { status: BookingStatus.ACCEPTED })
      .getRawOne();

    const NormalBookingsRevenue = await this.bookingRepository
      .createQueryBuilder("booking")
      .select("SUM(booking.amount)", "total")
      .where("booking.type = :type", { type: BookingType.CONSULTATION })
      .andWhere("booking.status = :status", { status: BookingStatus.ACCEPTED })
      .getRawOne();

    const NormalBookingServicesRevenue = await this.bookingRepository
      .createQueryBuilder("booking")
      .select("SUM(booking.amount)", "total")
      .where("booking.type = :type", { type: BookingType.SERVICE })
      .andWhere("booking.status = :status", { status: BookingStatus.ACCEPTED })
      .getRawOne();

    return {
      activeDoctorsCount,
      activeHospitalsCount,
      settlementRequests: {
        doctor: settlementCountDoctor,
        hospital: settlementCountHospital,
      },
      settlement: {
        total:
          settlementCountDoctor +
          settlementCountHospital +
          settlementCountApproved,
        requested: settlementCountDoctor + settlementCountHospital,
        approved: settlementCountApproved,
      },
      fast_tag: {
        count: fastTagBookingsCount,
        revenue: fastTagBookingsRevenue?.total || 0, // Handle null or undefined revenue
      },
      consultation: {
        revenue: NormalBookingsRevenue?.total || 0,
      },
      service: {
        revenue: NormalBookingServicesRevenue?.total || 0,
      },
    };
  }

  async getBookingCountsByMonth(
    filter: any,
  ): Promise<{ month: string; count: number }[]> {
    const statuses = [
      BookingStatus.COMPLETED,
      BookingStatus.ACCEPTED,
      BookingStatus.PENDING,
    ];

    const currentDate = moment();
    const startDate = currentDate
      .clone()
      .subtract(11, "months")
      .startOf("month"); // Start of 12 months ago
    const endDate = currentDate.clone().endOf("month"); // End of the current month

    const queryBuilder = this.bookingRepository
      .createQueryBuilder("booking")
      .select([
        "DATE_FORMAT(booking.appointmentDate, '%Y-%m') AS month",
        "COUNT(booking.id) AS count",
      ])
      .where("booking.status IN (:...statuses)", { statuses })
      .andWhere("booking.appointmentDate BETWEEN :startDate AND :endDate", {
        startDate: startDate.toDate(),
        endDate: endDate.toDate(),
      });

    // Apply hospital_id filter if present
    if (filter.hospital_id) {
      queryBuilder.andWhere("booking.hospital_id = :hospital_id", {
        hospital_id: filter.hospital_id,
      });
    }

    const bookings = await queryBuilder
      .groupBy("DATE_FORMAT(booking.appointmentDate, '%Y-%m')")
      .orderBy("DATE_FORMAT(booking.appointmentDate, '%Y-%m')", "ASC")
      .getRawMany();

    // Map the results to include month names
    const results = [];
    for (let i = 0; i < 12; i++) {
      const month = startDate.clone().add(i, "months");
      const monthKey = month.format("YYYY-MM");
      const booking = bookings.find((b) => b.month === monthKey);
      results.push({
        month: month.format("MMMM YYYY"),
        count: booking ? parseInt(booking.count, 10) : 0,
      });
    }

    return results;
  }

  async getHospitalAnalytics(hospital_id: string) {
    const fastTagBookingsCount = await this.bookingRepository.count({
      where: {
        type: BookingType.FAST_TAG,
        status: BookingStatus.ACCEPTED,
        hospital_id,
      },
    });

    const bookingsCount = await this.bookingRepository.count({
      where: {
        type: BookingType.CONSULTATION,
        status: In([
          BookingStatus.ACCEPTED,
          BookingStatus.COMPLETED,
          BookingStatus.STARTED,
        ]),
        hospital_id,
      },
    });

    const bookingsCountRequested = await this.bookingRepository.count({
      where: {
        type: BookingType.CONSULTATION,
        status: In([BookingStatus.PENDING, BookingStatus.STARTED]),
        hospital_id,
      },
    });

    // Count settlements requested for hospitals
    const settlementPending = await this.settlementRepository.sum("amount", {
      status: SettlementStatus.REQUESTED,
      hospital_id,
    });

    const requestedSettlementCount = await this.settlementRepository.count({
      where: {
        hospital_id,
        status: SettlementStatus.REQUESTED,
      },
    });

    const settlementHospitalApproved = await this.settlementRepository.sum(
      "amount",
      {
        hospital_id,
        status: SettlementStatus.APPROVED,
      },
    );
    const fastTagBookingsRevenue = await this.bookingRepository
      .createQueryBuilder("booking")
      .select("SUM(booking.amount)", "total")
      .where("booking.type = :type", { type: BookingType.FAST_TAG })
      .andWhere("booking.status = :status", { status: BookingStatus.ACCEPTED })
      .andWhere("booking.hospital_id = :hospital_id", { hospital_id })
      .getRawOne();

    const bookingsRevenue = await this.bookingRepository
      .createQueryBuilder("booking")
      .select("SUM(booking.amount)", "total")
      .where("booking.type = :type", { type: BookingType.CONSULTATION })
      .andWhere("booking.status = :status", { status: BookingStatus.ACCEPTED })
      .andWhere("booking.hospital_id = :hospital_id", { hospital_id })
      .getRawOne();

    // Count doctors in this hospital
    const doctorsCount = await this.userRepository.count({
      where: { hospital_id, user_type: "doctor" },
    });

    // Count staff in this hospital
    const staffCount = await this.userRepository.count({
      where: { hospital_id, user_type: "staff" },
    });
    const adminCount = await this.userRepository.count({
      where: { hospital_id, user_type: "hsAdmin" },
    });

    return {
      settlement: {
        pending: settlementPending ?? 0,
        approved: settlementHospitalApproved ?? 0,
        requested: requestedSettlementCount,
        total: settlementPending + settlementHospitalApproved,
      },
      fast_tag: {
        count: fastTagBookingsCount,
        revenue: Number(fastTagBookingsRevenue?.total) || 0,
      },
      booking: {
        request_count: bookingsCountRequested,
        count: bookingsCount,
        revenue: Number(bookingsRevenue?.total) || 0,
      },
      user_counts: {
        doctors_count: doctorsCount,
        staff_count: staffCount,
        admin_count: adminCount,
      },
    };
  }

  async getDoctorAnalytics(doctor_id: string) {
    // Count settlements requested for this doctor (pending/requested)
    const settlementCountDoctorPending = await this.settlementRepository.sum(
      "amount",
      {
        status: SettlementStatus.REQUESTED,
        doctor_id,
      },
    );

    // Total settlements for this doctor
    const settlementCountDoctorTotal = await this.bookingRepository.sum(
      "amount",
      {
        doctor_id,
        status: In[(BookingStatus.ACCEPTED, BookingStatus.COMPLETED)],
      },
    );

    // Approved settlements for this doctor
    const settlementCountDoctorApproved = await this.settlementRepository.sum(
      "amount",
      {
        doctor_id,
        status: SettlementStatus.APPROVED,
      },
    );
    const bookingCount = await this.bookingRepository.count({
      where: {
        doctor_id,
        status: BookingStatus.ACCEPTED,
      },
    });
    const bookingCountCompleted = await this.bookingRepository.count({
      where: {
        doctor_id,
        status: BookingStatus.COMPLETED,
      },
    });

    // You can add more doctor-specific analytics here if needed

    return {
      settlement: {
        pending: settlementCountDoctorPending ?? 0,
        requested: settlementCountDoctorApproved ?? 0,
        total: settlementCountDoctorTotal ?? 0,
      },
      booking: {
        count_remaining: bookingCount || 0,
        count_completed: bookingCountCompleted || 0,
      },
      // No fast_tag section for doctor analytics
    };
  }
}
