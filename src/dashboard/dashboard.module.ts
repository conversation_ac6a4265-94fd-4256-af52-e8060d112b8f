import { Modu<PERSON> } from "@nestjs/common";
import { DashboardService } from "./dashboard.service";
import { DashboardController } from "./dashboard.controller";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Doctor } from "src/doctors/entities/doctor.entity";
import { Booking } from "src/bookings/entities/booking.entity";
import { Hospital } from "src/hospitals/entities/hospital.entity";
import { Settlement } from "src/settlements/entities/settlement.entity";
import { User } from "src/users/entities/user.entity";

@Module({
  imports: [
    TypeOrmModule.forFeature([Doctor, Booking, Hospital, Settlement, User]),
  ],
  controllers: [DashboardController],
  providers: [DashboardService],
})
export class DashboardModule {}
