import { Module } from "@nestjs/common";
import { SpecialisationsService } from "./specialisations.service";
import { SpecialisationsController } from "./specialisations.controller";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Specialisations } from "./entities/specialisation.entity";

@Module({
  imports: [TypeOrmModule.forFeature([Specialisations])],
  controllers: [SpecialisationsController],
  providers: [SpecialisationsService],
  exports: [SpecialisationsService],
})
export class SpecialisationsModule {}
