import { Doctor } from "src/doctors/entities/doctor.entity";
import {
  <PERSON><PERSON>n,
  CreateDateColumn,
  Entity,
  ManyToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";

@Entity("specialisations")
export class Specialisations {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ unique: true })
  name: string;

  @Column({ nullable: true })
  image: string;

  @Column("text")
  description: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToMany(() => Doctor, (user) => user.specialisations)
  doctors: Doctor[];
}
