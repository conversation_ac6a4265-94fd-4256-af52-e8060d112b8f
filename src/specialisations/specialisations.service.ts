import { Injectable, NotFoundException } from "@nestjs/common";
import { CreateSpecialisationDto } from "./dto/create-specialisation.dto";
import { UpdateSpecialisationDto } from "./dto/update-specialisation.dto";
import { InjectRepository } from "@nestjs/typeorm";
import { Specialisations } from "./entities/specialisation.entity";
import { In, IsNull, Like, Not, Repository } from "typeorm";
import { paginate, PaginateQuery } from "nestjs-paginate";

@Injectable()
export class SpecialisationsService {
  constructor(
    @InjectRepository(Specialisations)
    private readonly specialisationsRepository: Repository<Specialisations>,
  ) {}
  async create(
    createSpecialisationDto: CreateSpecialisationDto,
  ): Promise<Specialisations> {
    const specialisation = this.specialisationsRepository.create(
      createSpecialisationDto,
    );
    return await this.specialisationsRepository.save(specialisation);
  }

  async findOne(id: string): Promise<Specialisations> {
    const specialisation = await this.specialisationsRepository.findOne({
      where: { id },
    });
    if (!specialisation) {
      throw new NotFoundException(`Specialisation with ID ${id} not found`);
    }
    return specialisation;
  }

  async findAll(query: PaginateQuery, filters: any) {
    let where: any = {
      id: Not(IsNull()),
    };

    // Filter by name
    if (filters.name) {
      where.name = Like(`%${filters.name}%`);
    }

    return paginate(query, this.specialisationsRepository, {
      sortableColumns: ["name"],
      searchableColumns: ["name"],
      defaultSortBy: [["name", "ASC"]],
      where,
    });
  }

  async findByIds(ids: string[]): Promise<Specialisations[]> {
    return await this.specialisationsRepository.findBy({
      id: In(ids),
    });
  }

  async update(
    id: string,
    updateSpecialisationDto: UpdateSpecialisationDto,
  ): Promise<Specialisations> {
    const specialisation = await this.findOne(id);
    Object.assign(specialisation, updateSpecialisationDto);
    return await this.specialisationsRepository.save(specialisation);
  }

  async remove(id: string): Promise<void> {
    const result = await this.specialisationsRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Specialisation with ID ${id} not found`);
    }
  }
}
