import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from "@nestjs/common";
import { SpecialisationsService } from "./specialisations.service";
import { CreateSpecialisationDto } from "./dto/create-specialisation.dto";
import { UpdateSpecialisationDto } from "./dto/update-specialisation.dto";
import { Paginate, PaginateQuery } from "nestjs-paginate";

@Controller("specialisations")
export class SpecialisationsController {
  constructor(
    private readonly specialisationsService: SpecialisationsService,
  ) {}

  @Post()
  create(@Body() createSpecialisationDto: CreateSpecialisationDto) {
    return this.specialisationsService.create(createSpecialisationDto);
  }

  @Get()
  findAll(@Paginate() query: PaginateQuery, @Query() filters: any) {
    return this.specialisationsService.findAll(query, filters);
  }

  @Get("user")
  findAllUser(@Paginate() query: PaginateQuery, @Query() filters: any) {
    return this.specialisationsService.findAll(query, filters);
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.specialisationsService.findOne(id);
  }

  @Patch(":id")
  async update(
    @Param("id") id: string,
    @Body() updateSpecialisationDto: UpdateSpecialisationDto,
  ) {
    return await this.specialisationsService.update(
      id,
      updateSpecialisationDto,
    );
  }

  @Delete(":id")
  async remove(@Param("id") id: string) {
    await this.specialisationsService.remove(id);
    return { message: "Specialisation deleted successfully" };
  }
}
