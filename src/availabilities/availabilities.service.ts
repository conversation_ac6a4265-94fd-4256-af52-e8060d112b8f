import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { MoreThanOrEqual, Repository } from "typeorm";
import { Availability, AvailabilityType } from "./entities/availability.entity";
import { CreateAvailabilityDto } from "./dto/create-availability.dto";
import { UpdateAvailabilityDto } from "./dto/update-availability.dto";
import { WeeksService } from "src/weeks/weeks.service";
import * as moment from "moment";

@Injectable()
export class AvailabilitiesService {
  constructor(
    @InjectRepository(Availability)
    private readonly availabilityRepository: Repository<Availability>,
    private readonly weekService: WeeksService,
  ) {}

  /**
   * Create a new availability or return an existing one if it matches the filters.
   */
  async create(createAvailabilityDto: CreateAvailabilityDto): Promise<any> {
    const { doctor_id, not_available, week_id, type } = createAvailabilityDto;

    // Dynamically build the `where` condition using only defined keys from DTO
    const where: any = { doctor_id };
    for (const key of Object.keys(createAvailabilityDto)) {
      if (
        createAvailabilityDto[key] !== undefined &&
        createAvailabilityDto[key] !== null
      ) {
        where[key] = createAvailabilityDto[key];
      }
    }

    if (week_id && not_available) {
      await this.availabilityRepository.delete({
        doctor_id: doctor_id,
        week_id: week_id,
      });
      return {
        status: true,
        message: "Availability created successfully",
      };
    }
    // Check if an availability with the same filters already exists
    const existing = await this.availabilityRepository.findOne({ where });
    if (existing) {
      return {
        status: true,
        message: "Availability created successfully",
      };
    }

    // Create and save a new availability
    const newEntry = this.availabilityRepository.create(createAvailabilityDto);
    if (type === AvailabilityType.WEEK) {
      newEntry.date = null;
    }
    if (type === AvailabilityType.DATE) {
      newEntry.week_id = null;
    }
    if (not_available) {
      newEntry.startTime = null;
      newEntry.endTime = null;
    }
    await this.availabilityRepository.save(newEntry);
    return {
      status: true,
      message: "Availability created successfully",
    };
  }

  /**
   * Find weeks with availabilities for a specific doctor.
   */
  async findWeeksWithAvailabilities(
    doctor_id: string,
    filters: any,
  ): Promise<any> {
    const data = await this.weekService.findWeeksWithAvailabilities(
      doctor_id,
      filters,
    );
    return {
      status: true,
      message: "Weeks with availabilities fetched successfully",
      data: data,
    };
  }
  /**
}
  }

  /**
   * Find availabilities for a specific doctor by date.
   * If no date is provided, filter for dates greater than or equal to today's start of the day.
   */
  async findDateWithAvailabilities(
    doctor_id: string,
    filter: any,
  ): Promise<any> {
    const where: any = {
      doctor_id: doctor_id,
      type: AvailabilityType.DATE,
    };

    if (filter.date) {
      // Apply the specific date filter
      where.date = filter.date;
    } else {
      // Apply the filter for dates greater than or equal to today's start of the day
      const todayStart = moment().startOf("day").toDate();
      where.date = MoreThanOrEqual(todayStart);
    }

    const data = await this.availabilityRepository.find({
      where: where,
      order: {
        date: "ASC", // Sort by date in ascending order
      },
    });
    return {
      status: true,
      message: "Date with availabilities fetched successfully",
      data: data,
    };
  }

  /**
   * Find a single availability by ID.
   */
  async findOne(id: string): Promise<Availability> {
    const availability = await this.availabilityRepository.findOne({
      where: { id },
      relations: ["doctor", "week"],
    });
    if (!availability) {
      throw new NotFoundException(`Availability with ID ${id} not found`);
    }
    return availability;
  }

  /**
   * Update an existing availability by ID.
   */
  async update(
    id: string,
    updateAvailabilityDto: UpdateAvailabilityDto,
  ): Promise<Availability> {
    const availability = await this.findOne(id);
    Object.assign(availability, updateAvailabilityDto);
    return this.availabilityRepository.save(availability);
  }

  /**
   * Remove an availability by ID.
   */
  async remove(id: string): Promise<any> {
    const availability = await this.findOne(id);
    await this.availabilityRepository.remove(availability);
    return {
      status: true,
      message: "Availability removed successfully",
    };
  }

  /**
   * Add availabilities for all weeks for a specific doctor.
   */
  async addAvailability(doctorId: string): Promise<boolean> {
    // Fetch all weeks
    const weeks = await this.weekService.findAll();

    for (const week of weeks) {
      // Check if availability for this week and doctor already exists
      const existingAvailability = await this.availabilityRepository.findOne({
        where: {
          week_id: week.id,
          doctor_id: doctorId,
          type: AvailabilityType.WEEK,
        },
      });

      // If no existing availability, create a new one
      if (!existingAvailability) {
        const newAvailability = this.availabilityRepository.create({
          week_id: week.id,
          doctor_id: doctorId,
          date: null,
          startTime: "09:00",
          endTime: "17:00",
          type: AvailabilityType.WEEK,
        });

        await this.availabilityRepository.save(newAvailability);
      }
    }

    return true;
  }

  /**
   * Update an availability by ID.
   */
  async updateAvailability(
    id: string,
    updateAvailabilityDto: UpdateAvailabilityDto,
  ): Promise<any> {
    // Find the availability by ID
    const availability = await this.findOne(id);

    if (!availability) {
      throw new NotFoundException(`Availability with ID ${id} not found`);
    }

    // Update the availability fields
    Object.assign(availability, updateAvailabilityDto);

    // Handle specific cases based on the type and not_available flag
    if (updateAvailabilityDto.type === AvailabilityType.WEEK) {
      availability.date = null; // Clear the date if type is WEEK
    }

    if (updateAvailabilityDto.type === AvailabilityType.DATE) {
      availability.week_id = null; // Clear the week_id if type is DATE
    }

    if (updateAvailabilityDto.not_available) {
      availability.startTime = null; // Clear startTime if not_available is true
      availability.endTime = null; // Clear endTime if not_available is true
    }

    // Save the updated availability
    const updatedAvailability =
      await this.availabilityRepository.save(availability);

    return {
      status: true,
      message: "Availability updated successfully",
      data: updatedAvailability,
    };
  }

  /**
   * Fetch availabilities for a specific doctor by doctor_id and either week_id or date.
   */
  async findDoctorAvailabilities(
    doctor_id: string,
    filter: { week_id?: string; date?: Date },
  ): Promise<any> {
    const where: any = { doctor_id };

    // Apply week_id filter if provided
    if (filter.week_id) {
      where.week_id = filter.week_id;
      where.type = AvailabilityType.WEEK;
    }

    // Apply date filter if provided
    if (filter.date) {
      where.date = filter.date;
      where.type = AvailabilityType.DATE;
    }

    return await this.availabilityRepository.find({
      where,
      order: {
        startTime: "ASC", // Sort by start time
      },
    });
  }
}
