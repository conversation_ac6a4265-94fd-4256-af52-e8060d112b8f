import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from "@nestjs/common";
import { AvailabilitiesService } from "./availabilities.service";
import { CreateAvailabilityDto } from "./dto/create-availability.dto";
import { UpdateAvailabilityDto } from "./dto/update-availability.dto";

@Controller("availabilities")
export class AvailabilitiesController {
  constructor(private readonly availabilitiesService: AvailabilitiesService) {}

  @Post()
  async create(@Body() createAvailabilityDto: CreateAvailabilityDto) {
    return this.availabilitiesService.create(createAvailabilityDto);
  }

  @Get("doctors/:id/week")
  async findWeeksWithAvailabilities(
    @Param("id") id: string,
    @Query() filters: any,
  ) {
    return this.availabilitiesService.findWeeksWithAvailabilities(id, filters);
  }

  @Get("doctors/:id/date")
  async findDateWithAvailabilities(
    @Param("id") id: string,
    @Query() filters: any,
  ) {
    return this.availabilitiesService.findDateWithAvailabilities(id, filters);
  }

  @Get(":id")
  async findOne(@Param("id") id: string) {
    return this.availabilitiesService.findOne(id);
  }

  @Patch(":id")
  async update(
    @Param("id") id: string,
    @Body() updateAvailabilityDto: UpdateAvailabilityDto,
  ) {
    return this.availabilitiesService.updateAvailability(
      id,
      updateAvailabilityDto,
    );
  }

  @Delete(":id")
  async remove(@Param("id") id: string) {
    return this.availabilitiesService.remove(id);
  }
}
