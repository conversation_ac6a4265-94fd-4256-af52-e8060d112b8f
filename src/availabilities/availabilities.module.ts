import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AvailabilitiesService } from "./availabilities.service";
import { AvailabilitiesController } from "./availabilities.controller";
import { Availability } from "./entities/availability.entity";
import { WeeksModule } from "src/weeks/weeks.module";

@Module({
  imports: [TypeOrmModule.forFeature([Availability]), WeeksModule],
  controllers: [AvailabilitiesController],
  providers: [AvailabilitiesService],
  exports: [AvailabilitiesService],
})
export class AvailabilitiesModule {}
