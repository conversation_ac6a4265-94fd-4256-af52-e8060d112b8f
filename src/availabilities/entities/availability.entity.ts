import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  Column,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import { Doctor } from "src/doctors/entities/doctor.entity";
import { Week } from "src/weeks/entities/week.entity";

// Define the enum for type
export enum AvailabilityType {
  WEEK = "week",
  DATE = "date",
}

@Entity("availabilities")
export class Availability {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 36 })
  doctor_id: string;

  @Column({ type: "varchar", length: 36, nullable: true })
  week_id: string | null;

  @Column({ type: "enum", enum: AvailabilityType })
  type: AvailabilityType;

  @Column({ type: "date", nullable: true })
  date: Date | null;

  @Column({ type: "time", nullable: true })
  startTime: string | null;

  @Column({ type: "time", nullable: true })
  endTime: string | null;

  @Column({ type: "boolean", default: false })
  not_available: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => Doctor, { onDelete: "CASCADE" })
  @JoinColumn({ name: "doctor_id" })
  doctor: Doctor;

  @ManyToOne(() => Week, { onDelete: "CASCADE", nullable: true })
  @JoinColumn({ name: "week_id" })
  week: Week | null;
}
