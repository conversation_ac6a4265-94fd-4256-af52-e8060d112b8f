import {
  IsString,
  IsEnum,
  IsOptional,
  IsBoolean,
  IsDateString,
  IsNotEmpty,
  ValidateIf,
} from "class-validator";
import { AvailabilityType } from "../entities/availability.entity";

export class CreateAvailabilityDto {
  @IsNotEmpty()
  @IsString()
  doctor_id: string;

  @ValidateIf((o) => o.type === AvailabilityType.WEEK) // Validate only if type is "week"
  @IsNotEmpty()
  @IsString()
  week_id?: string;

  @IsNotEmpty()
  @IsEnum(AvailabilityType)
  type: AvailabilityType;

  @ValidateIf((o) => o.type === AvailabilityType.DATE) // Validate only if type is "date"
  @IsNotEmpty()
  @IsDateString()
  date?: string;

  @ValidateIf((o) => o.not_available === false) // Validate only if not_available is false
  @IsNotEmpty()
  @IsString()
  startTime?: string;

  @ValidateIf((o) => o.not_available === false) // Validate only if not_available is false
  @IsNotEmpty()
  @IsString()
  endTime?: string;

  @IsOptional()
  @IsBoolean()
  not_available?: boolean;
}
