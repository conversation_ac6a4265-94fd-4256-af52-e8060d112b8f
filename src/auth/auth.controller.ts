import {
  <PERSON>,
  Post,
  Body,
  Get,
  Headers,
  BadRequestException,
  UseGuards,
  Req,
} from "@nestjs/common";
import { AuthService } from "./auth.service";
import { PhoneVerifyDto, SignInDto, VerifyOtpDto } from "./dto/signIn.dto";
import { AccessTokenGuard } from "src/common/guards/accessToken.guard";

@Controller("auth")
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post("login")
  signIn(@Body() signInDto: SignInDto, @Headers() headers: any) {
    return this.authService.signIn(signInDto, headers);
  }

  @Post("login/user")
  async verifyPhone(
    @Body() phoneVerifyDto: PhoneVerifyDto,
    @Headers("app-type") app_type: any,
  ) {
    if (!app_type) {
      throw new BadRequestException(
        "app-type in header is required values: user or doctor",
      );
    }
    return await this.authService.generateOtp(phoneVerifyDto, app_type);
  }

  @Post("verify-otp")
  async verifyOtp(@Body() verifyOtpDto: VerifyOtpDto) {
    return this.authService.verifyOtp(verifyOtpDto);
  }

  @Get("me")
  @UseGuards(AccessTokenGuard)
  async me(@Req() req: Request) {
    return this.authService.me(req["uid"]);
  }

  @Post("forgot-password/request")
  async forgotPasswordRequest(@Body("email") email: string) {
    if (!email) throw new BadRequestException("Email is required");
    return this.authService.forgotPasswordRequest(email);
  }

  @Post("forgot-password/verify")
  async forgotPasswordVerify(@Body() body: { user_id: string; otp: string }) {
    if (!body.user_id || !body.otp)
      throw new BadRequestException("user_id and OTP are required");
    return this.authService.forgotPasswordVerifyOtp(body.user_id, body.otp);
  }

  @Post("forgot-password/reset")
  async forgotPasswordReset(
    @Body()
    body: {
      user_id: string;
      newPassword: string;
      confirmPassword: string;
    },
  ) {
    if (!body.user_id || !body.newPassword || !body.confirmPassword)
      throw new BadRequestException("All fields are required");
    return this.authService.forgotPasswordReset(
      body.user_id,
      body.newPassword,
      body.confirmPassword,
    );
  }
}
