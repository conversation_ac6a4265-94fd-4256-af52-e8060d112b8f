import {
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsPhoneNumber,
  IsString,
} from "class-validator";

export class SignInDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  password: string;
}

export class PhoneVerifyDto {
  @IsPhoneNumber("IN")
  phone_number: string;
}

export class VerifyOtpDto {
  @IsString()
  @IsNotEmpty()
  user_id: string;

  @IsString()
  @IsNotEmpty()
  otp: string;

  @IsBoolean()
  @IsOptional()
  new_user?: boolean;
}
