import { <PERSON>du<PERSON> } from "@nestjs/common";
import { AuthService } from "./auth.service";
import { AuthController } from "./auth.controller";
import { JwtModule } from "@nestjs/jwt";
import { UsersModule } from "src/users/users.module";
import { CommonModule } from "src/common/common.module";
import { AuthTokenervice } from "./services/auth-token.service";
import { DoctorsModule } from "src/doctors/doctors.module";
import { NotificationsModule } from "src/notifications/notifications.module";

@Module({
  imports: [
    JwtModule.register({}),
    UsersModule,
    CommonModule,
    DoctorsModule,
    NotificationsModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, AuthTokenervice],
})
export class AuthModule {}
