import {
  BadRequestException,
  Injectable,
  NotFoundException,
  Query,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { District } from "./entities/districts.entity";
import { paginate, PaginateQuery } from "nestjs-paginate";

@Injectable()
export class DistrictsService {
  constructor(
    @InjectRepository(District)
    private readonly districtRepository: Repository<District>,
  ) {}

  async findAll(query: PaginateQuery) {
    return paginate(query, this.districtRepository, {
      sortableColumns: ["name"],
      searchableColumns: ["name"],
      maxLimit: 20,
    });
  }

  async findById(id?: string): Promise<District> {
    const district = await this.districtRepository.findOne({ where: { id } });
    if (!district) {
      throw new NotFoundException(`District with ID ${id} not found`);
    }
    return district;
  }
}
