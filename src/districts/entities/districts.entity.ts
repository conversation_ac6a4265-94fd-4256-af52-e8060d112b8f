import { Hospital } from "src/hospitals/entities/hospital.entity";
import { User } from "src/users/entities/user.entity";
import { Entity, Column, PrimaryGeneratedColumn, OneToMany } from "typeorm";

@Entity("districts")
export class District {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ unique: true })
  name: string;

  @Column({ unique: true })
  slug: string;

  @OneToMany(() => User, (user) => user.district)
  users: User[];

  @OneToMany(() => Hospital, (hospital) => hospital.district)
  hospitals: Hospital[];
}
