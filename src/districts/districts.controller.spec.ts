import { Test, TestingModule } from "@nestjs/testing";
import { DistrictsController } from "./districts.controller";
import { DistrictsService } from "./districts.service";

describe("DistrictsController", () => {
  let controller: DistrictsController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DistrictsController],
      providers: [DistrictsService],
    }).compile();

    controller = module.get<DistrictsController>(DistrictsController);
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });
});
