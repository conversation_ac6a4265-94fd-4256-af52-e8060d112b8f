import { Controller, Get } from "@nestjs/common";
import { DistrictsService } from "./districts.service";
import { District } from "./entities/districts.entity";
import { Paginate, PaginateQuery } from "nestjs-paginate";

@Controller("districts")
export class DistrictsController {
  constructor(private readonly districtsService: DistrictsService) {}

  @Get()
  async findAll(@Paginate() query: PaginateQuery) {
    return this.districtsService.findAll(query);
  }
}
