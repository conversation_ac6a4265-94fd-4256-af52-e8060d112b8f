import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Between, In, IsNull, Not, Repository } from "typeorm";
import { Booking, BookingStatus, UserDetail } from "./entities/booking.entity";
import { CreateBookingDto } from "./dto/create-booking.dto";
import { UpdateBookingDto } from "./dto/update-booking.dto";
import { ResponseService } from "../common/services/response.service";
import { DoctorsService } from "src/doctors/doctors.service";
import { HospitalsService } from "src/hospitals/hospitals.service";
import { TimeSlotService } from "src/time-slots/time-slots.service";
import {
  FilterOperator,
  FilterSuffix,
  paginate,
  PaginateQuery,
} from "nestjs-paginate";
import { TransactionsService } from "src/transactions/transactions.service";
import { Doctor } from "src/doctors/entities/doctor.entity";
import { CashfreeService } from "src/cashfree/cashfree.service";
import { Transaction } from "src/transactions/entities/transaction.entity";
import { Gender, User } from "src/users/entities/user.entity";
import { UsersService } from "src/users/users.service";
import { identity } from "rxjs";
import { CreateHospitalServiceDto } from "src/hospital-services/dto/create-hospital-service.dto";
import { CreateServiceBookingDto } from "./dto/create-service-booking.dto";
import { CreateFastTagBookingDto } from "./dto/create-fasttag-bookings";
import { BookingType, TransactionStatus } from "src/common/enums/status.enum";
import { HospitalServicesService } from "src/hospital-services/hospital-services.service";
import { CalculateAmountDto } from "./dto/calculate-amount-dto";
import { CouponUsageService } from "src/coupons/services/coupon-usage.service";
import { CouponsService } from "src/coupons/coupons.service";
import { create } from "domain";
import { GetBookingDetailsDto } from "./dto/get-bookingCount.dto";

@Injectable()
export class BookingService {
  private readonly commission: number;

  constructor(
    @InjectRepository(Booking)
    private bookingRepository: Repository<Booking>,
    private responseService: ResponseService,
    private doctorService: DoctorsService,
    private hospitalService: HospitalsService,
    private timeSlotService: TimeSlotService,
    @Inject(forwardRef(() => TransactionsService))
    private transactionsService: TransactionsService,
    private cashFreeservice: CashfreeService,
    private userService: UsersService,
    @Inject(forwardRef(() => HospitalServicesService))
    private hospitalServiceServices: HospitalServicesService,

    private readonly couponsService: CouponsService,
    private readonly couponUsageService: CouponUsageService,
  ) {
    // Initialize the commission value from the environment or default to 10%
    this.commission = Number(process.env.COMMISSION || 10); // 10% commission
  }

  /**
   * Generates a unique booking ID using hospital's first two letters and a sequential number
   */
  private async generatebooking_id(hospital_id: string): Promise<string> {
    const hospital = await this.hospitalService.findById(hospital_id);
    if (!hospital) {
      throw new NotFoundException("Hospital not found");
    }

    const prefix = hospital.name.substring(0, 2).toUpperCase();
    const count = await this.bookingRepository.count({
      where: { hospital: { id: hospital_id } },
    });

    return `${prefix}${(count + 1).toString().padStart(4, "0")}`;
  }

  /**
   * Creates a new booking for a user
   */
  async createBooking(uid: string, createBookingDto: CreateBookingDto) {
    const { appointmentDate, is_online, user_id, coupon_id, amount } =
      createBookingDto;

    // try {
    const doctor = await this.doctorService.findById(
      createBookingDto.doctor_id,
    );
    if (!doctor) {
      return this.responseService.errorResponse("Doctor not found");
    }
    const user = await this.userService.findOneByParam({ id: uid });
    // Check if time slot is available
    let timeSlot = createBookingDto.timeSlot ?? null;
    if (timeSlot) {
      const isSlotAvailable = await this.timeSlotService.checkSlotAvailability(
        doctor.id,
        doctor.hospital_id,
        createBookingDto.appointmentDate,
        timeSlot,
      );
      if (!isSlotAvailable) {
        return this.responseService.errorResponse("Time slot is not available");
      }
    } else if (doctor.auto_booking_enabled === true) {
      const slot = await this.timeSlotService.findFirstAvailableSlot(
        doctor,
        appointmentDate,
      );
      timeSlot = slot?.startTime;
      if (!timeSlot) {
        return this.responseService.errorResponse("Time slot is not available");
      }
    }

    const booking_id = await this.generatebooking_id(
      createBookingDto.hospital_id,
    );

    const booking = this.bookingRepository.create({
      // ...createBookingDto,
      booking_id,
      user_id: user_id ? user_id : uid,
      created_by: uid,
      amount,
      appointmentDate: createBookingDto.appointmentDate,
      timeSlot: createBookingDto.timeSlot ?? null,
      hospital_id: createBookingDto.hospital_id ?? null,
      doctor_id: createBookingDto.doctor_id,
      user_details: createBookingDto.user_details ?? null,
      child_user_id: createBookingDto.child_user_id ?? null,
      meta_data: createBookingDto.meta_data ?? null,
      reason: createBookingDto.reason ?? null,
      status: BookingStatus.STARTED,
      doctor: { id: createBookingDto.doctor_id },
      hospital: { id: createBookingDto.hospital_id },
      is_fast_tag: createBookingDto.hospital_id
        ? await this.isFastTagBookingExists(
            user_id,
            createBookingDto.hospital_id,
            createBookingDto.appointmentDate,
          )
        : false,
    });

    const savedBooking = await this.bookingRepository.save(booking);

    // Mark time slot as booked
    if (createBookingDto.timeSlot) {
      await this.timeSlotService.markSlotAsBooked(
        createBookingDto.doctor_id,
        createBookingDto.appointmentDate,
        createBookingDto.timeSlot,
      );
    }

    const transaction = await this.createTransaction(savedBooking, coupon_id);

    const paymentOrder = await this.createPaymentOrder(transaction, user);
    transaction.order_id = paymentOrder.order_id;
    await this.transactionsService.saveInstance(transaction);
    await this.updateTokenNumber(
      createBookingDto.doctor_id,
      createBookingDto.appointmentDate,
      BookingType.CONSULTATION,
    );

    return this.responseService.successResponse(
      "Booking created successfully",
      { transaction_id: transaction.id, booking: savedBooking, paymentOrder },
    );
    // } catch (error) {
    //   return this.responseService.errorResponse(
    //     "Failed to create booking: " + error.message,
    //   );
    // }
  }
  async createTransaction(booking: Booking, coupon_id?: string) {
    const transaction = await this.transactionsService.initiateInstance();
    transaction.user_id = booking.user_id;
    transaction.booking_id = booking.id;
    transaction.doctor_id = booking.doctor_id ?? null;
    transaction.hospital_id = booking.hospital_id ?? null;
    transaction.hospital_service_id = booking.hospital_service_id ?? null;
    transaction.amount = booking.amount; // Set the amount as needed
    transaction.status = TransactionStatus.STARTED;
    transaction.paid_date = new Date();
    transaction.type = booking.type;
    transaction.coupon_id = coupon_id ?? null;
    transaction.is_online = booking.is_online;
    transaction.payment_type = booking.payment_type;
    return await this.transactionsService.saveInstance(transaction);
  }

  async createPaymentOrder(transaction: Transaction, user: User) {
    const order = await this.cashFreeservice.createOrder(user, transaction);
    return order;
  }
  /**
   * Updates a booking status and prescription
   */
  async updateBooking(
    booking_id: string,
    updateBookingDto: UpdateBookingDto,
    actorId: string,
  ) {
    try {
      const booking = await this.bookingRepository.findOne({
        where: { booking_id },
        relations: ["doctor", "hospital"],
      });

      if (!booking) {
        return this.responseService.errorResponse("Booking not found");
      }

      // // Authorization check
      // if (role === "DOCTOR" && booking.doctor.id !== actorId) {
      //   return this.responseService.errorResponse(
      //     "You can only update your own bookings",
      //   );
      // }

      // if (role === "HOSPITAL_ADMIN" && booking.hospital.id !== actorId) {
      //   return this.responseService.errorResponse(
      //     "You can only update bookings from your hospital",
      //   );
      // }

      Object.assign(booking, updateBookingDto);
      const updatedBooking = await this.bookingRepository.save(booking);

      return this.responseService.successResponse(
        "Booking updated successfully",
        updatedBooking,
      );
    } catch (error) {
      return this.responseService.errorResponse(
        "Failed to update booking: " + error.message,
      );
    }
  }

  /**
   * Gets all bookings for a user
   */
  async getUserBookings(userId: string, query: PaginateQuery, filters: any) {
    const where = await this.buildWhereFilters(filters);
    where.user_id = userId; // Ensure the user_id is always included

    return paginate(query, this.bookingRepository, {
      sortableColumns: ["id"],
      where,
      relations: ["hospital", "doctor", "hospitalService"],
      defaultSortBy: [["id", "DESC"]],
      searchableColumns: ["booking_id"],
      filterableColumns: {
        first_name: [FilterOperator.EQ, FilterSuffix.NOT],
      },
    });
  }

  /**
   * Gets all bookings for a doctor
   */
  async getDoctorBookings(
    doctor_id: string,
    query: PaginateQuery,
    filters: any,
  ) {
    const where = await this.buildWhereFilters(filters);
    where.doctor_id = doctor_id; // Ensure the doctor_id is always included

    return paginate(query, this.bookingRepository, {
      sortableColumns: ["id"],
      where,
      relations: ["user"],
      defaultSortBy: [["id", "DESC"]],
      searchableColumns: ["booking_id"],
      filterableColumns: {
        first_name: [FilterOperator.EQ, FilterSuffix.NOT],
      },
    });
  }

  /**
   * Gets all bookings for a hospital
   */
  async getHospitalBookings(
    hospital_id: string,
    query: PaginateQuery,
    filters: any,
  ) {
    const where = await this.buildWhereFilters(filters);
    where.hospital_id = hospital_id; // Ensure the hospital_id is always included

    return paginate(query, this.bookingRepository, {
      sortableColumns: ["id"],
      where,
      relations: ["user", "hospital", "doctor.departments"],
      defaultSortBy: [["id", "DESC"]],
      searchableColumns: ["booking_id"],
      filterableColumns: {
        first_name: [FilterOperator.EQ, FilterSuffix.NOT],
      },
    });
  }

  /**
   * Calculates the total amount including the platform commission
   */

  async calculateCommission(pricing: number): Promise<number> {
    return (pricing * this.commission) / 100;
  }
  async amountCalculate(amount: number): Promise<number> {
    const pricing = Number(amount); // Convert to number
    const total = pricing + (await this.calculateCommission(amount)); // Add commission
    return Number(total.toFixed(2)); // Round to 2 decimal places
  }

  async calculateAmount(
    user_id: string,
    calculateAmountDto: CalculateAmountDto,
  ) {
    const {
      is_fast_tag,
      hospital_id,
      doctor_id,
      hospital_service_id,
      coupon_id,
    } = calculateAmountDto;

    let amount = 0;
    let coupon = null;
    let discount = 0;
    if (coupon_id) {
      coupon = await this.couponsService.findCouponById(coupon_id);
    }
    if (is_fast_tag) {
      if (!hospital_id) {
        throw new NotFoundException(
          "Hospital ID is required for fast tag bookings",
        );
      }

      const hospital = await this.hospitalService.findById(hospital_id);
      if (
        !hospital ||
        !hospital.fastTag ||
        hospital.fastTag.enabled == false ||
        hospital.fastTag.price == 0
      ) {
        throw new NotFoundException(
          "Fast Tag is not available for this hospital",
        );
      }

      amount = Number(hospital.fastTag.price);
    } else {
      if (doctor_id) {
        const doctor = await this.doctorService.findById(doctor_id);
        if (!doctor) {
          throw new NotFoundException("Doctor not found");
        }
        amount = doctor.pricing;
      } else if (hospital_service_id) {
        const hospitalService =
          await this.hospitalServiceServices.findOneById(hospital_service_id);
        if (!hospitalService) {
          throw new NotFoundException("Hospital service not found");
        }
        amount = Number(hospitalService.price);
      } else {
        throw new NotFoundException(
          "Doctor ID or Hospital Service ID is required",
        );
      }
    }
    let total = await this.amountCalculate(amount);
    if (coupon) {
      discount = await this.couponUsageService.calculateCouponDiscount(
        coupon,
        total,
      );
      if (discount > 0) {
        total -= discount;
      }
    }
    return {
      fee: Number(amount),
      platform_fee: await this.calculateCommission(amount),
      total,
      discount,
      currency: "INR",
      coupon_code: coupon ? coupon.coupon_code : null,
      coupon_id: coupon ? coupon.id : null,
    };
  }

  async saveInstance(booking: Booking): Promise<Booking> {
    return await this.bookingRepository.save(booking);
  }

  async findById(id: string): Promise<Booking> {
    const booking = await this.bookingRepository.findOne({
      where: { id },
    });
    if (!booking) {
      throw new NotFoundException("Booking not found");
    }
    return booking;
  }

  async updateTimeSlot(
    booking_id: string,
    updateBookingDto: UpdateBookingDto,
    user_id: string,
  ) {
    const booking = await this.bookingRepository.findOne({
      where: { id: booking_id },
    });
    if (!booking) {
      return this.responseService.errorResponse("Booking not found");
    }
    const isSlotAvailable = await this.timeSlotService.checkSlotAvailability(
      booking.doctor_id,
      booking.hospital_id,
      booking.appointmentDate,
      updateBookingDto.timeSlot,
    );

    if (!isSlotAvailable) {
      return this.responseService.errorResponse("Time slot is not available");
    }
    await this.bookingRepository.update(booking.id, {
      timeSlot: updateBookingDto.timeSlot,
      status: BookingStatus.ACCEPTED,
    });

    await this.timeSlotService.markSlotAsBooked(
      booking.doctor_id,
      booking.appointmentDate,
      updateBookingDto.timeSlot,
    );
    await this.updateTokenNumber(
      booking.doctor_id,
      booking.appointmentDate,
      BookingType.CONSULTATION,
    );

    return this.responseService.successResponse(
      "Booking updated successfully",
      { booking },
    );
  }

  async bookHospitalService(
    createBookingDto: CreateServiceBookingDto,
    user_id: string,
  ) {
    const { hospital_id, coupon_id } = createBookingDto;
    const user = await this.userService.findOneByParam({ id: user_id });

    const hospital = await this.hospitalService.findById(hospital_id);
    if (!hospital) {
      return this.responseService.errorResponse("Hospital not found");
    }
    const booking_id = await this.generatebooking_id(
      createBookingDto.hospital_id,
    );
    const booking = this.bookingRepository.create({
      user_id,
      status: BookingStatus.STARTED,
      type: BookingType.SERVICE,
      hospital_id,
      booking_id,
      reason: createBookingDto.reason,
      appointmentDate: createBookingDto.appointmentDate,
      timeSlot: createBookingDto.timeSlot,
      hospital_service_id: createBookingDto.hospital_service_id,
      amount: createBookingDto.amount,
      child_user_id: createBookingDto.child_user_id,
      meta_data: createBookingDto.meta_data ?? null,
      created_by: user_id,
      is_fast_tag: hospital_id
        ? await this.isFastTagBookingExists(
            user_id,
            hospital_id,
            createBookingDto.appointmentDate,
          )
        : false,
    });

    const savedBooking = await this.bookingRepository.save(booking);
    const transaction = await this.createTransaction(savedBooking, coupon_id);

    const paymentOrder = await this.createPaymentOrder(transaction, user);
    transaction.order_id = paymentOrder.order_id;
    await this.transactionsService.saveInstance(transaction);

    // await this.updateTokenNumber(createBookingDto.hospital_service_id, createBookingDto.appointmentDate, BookingType.SERVICE);
    return this.responseService.successResponse(
      "Booking created successfully",
      { transaction_id: transaction.id, booking: savedBooking, paymentOrder },
    );
  }

  async bookFastTag(
    CreateFastTagBookingDto: CreateFastTagBookingDto,
    user_id: string,
  ) {
    const { hospital_id, coupon_id, amount } = CreateFastTagBookingDto;
    const bookingExists = await this.bookingRepository.findOne({
      where: {
        user_id,
        hospital_id,
        type: BookingType.FAST_TAG,
      },
    });
    if (bookingExists) {
      return this.responseService.errorResponse(
        "Booking already exists for this hospital",
      );
    }
    const hospital = await this.hospitalService.findById(hospital_id);
    const fast_tag = hospital.fastTag;

    const bookingCount = await this.bookingRepository.count({
      where: {
        hospital_id,
        type: BookingType.FAST_TAG,
        appointmentDate: CreateFastTagBookingDto.appointmentDate,
        status: BookingStatus.ACCEPTED,
      },
    });

    if (
      !fast_tag ||
      fast_tag.enabled == false ||
      fast_tag.price <= 0 ||
      bookingCount >= fast_tag.count
    ) {
      return this.responseService.errorResponse("Fast Tag not available");
    }
    if (!hospital) {
      return this.responseService.errorResponse("Hospital not found");
    }
    const booking_id = await this.generatebooking_id(
      CreateFastTagBookingDto.hospital_id,
    );
    const booking = this.bookingRepository.create({
      user_id,
      status: BookingStatus.STARTED,
      type: BookingType.FAST_TAG,
      hospital_id,
      booking_id,
      amount,
      appointmentDate: CreateFastTagBookingDto.appointmentDate,
      child_user_id: CreateFastTagBookingDto.child_user_id,
      meta_data: CreateFastTagBookingDto.meta_data ?? null,
      created_by: user_id,
    });
    const savedBooking = await this.bookingRepository.save(booking);
    const transaction = await this.createTransaction(savedBooking, coupon_id);

    const user = await this.userService.findOneByParam({ id: user_id });

    const paymentOrder = await this.createPaymentOrder(transaction, user);
    transaction.order_id = paymentOrder.order_id;
    await this.transactionsService.saveInstance(transaction);
    return this.responseService.successResponse(
      "Booking created successfully",
      { transaction_id: transaction.id, booking: savedBooking, paymentOrder },
    );
  }

  async isFastTagBookingExists(
    user_id: string,
    hospital_id: string,
    date: Date,
  ): Promise<boolean> {
    const booking = await this.bookingRepository.findOne({
      where: {
        user_id,
        hospital_id,
        type: BookingType.FAST_TAG,
        appointmentDate: date,
        status: BookingStatus.ACCEPTED,
      },
    });
    return !!booking;
  }

  async getBookingDetails(dto: GetBookingDetailsDto) {
    const { user_id, hospital_id, hospital_service_id, date } = dto;

    // Format the date using moment

    const statuses = [
      BookingStatus.PENDING,
      BookingStatus.ACCEPTED,
      BookingStatus.COMPLETED,
    ];
    const formattedDate = new Date(date);
    formattedDate.setHours(0, 0, 0, 0); // Set time to 00:00:00
    console.log(formattedDate);
    // Query to count total bookings
    const total_bookings = await this.bookingRepository.find({
      where: {
        appointmentDate: formattedDate,
        status: In(statuses),
        ...(hospital_service_id
          ? {
              hospital_service_id,
              type: BookingType.SERVICE,
            } // If hospital_service_id exists, filter by it
          : {
              hospital_id,
              type: BookingType.FAST_TAG,
            }), // Otherwise, filter by hospital_id
      },
    });

    // Query to check if the user has already booked
    const fast_tag_booked = !!(await this.bookingRepository.findOne({
      where: {
        user_id,
        appointmentDate: formattedDate,
        type: BookingType.FAST_TAG,
        status: In(statuses),
      },
    }));

    return { total_bookings: total_bookings.length, fast_tag_booked };
  }

  async buildWhereFilters(filters: any) {
    const where: any = {};

    if (filters.status) {
      const statuses = filters.status.split(",").map((status) => status.trim());
      where.status = In(statuses);
    }

    if (filters.user_id) {
      where.user_id = filters.user_id;
    }

    if (filters.child_user_id) {
      where.child_user_id = filters.child_user_id;
    }

    if (filters.type) {
      where.type = filters.type;
    }

    if (filters.hospital_id) {
      where.hospital_id = filters.hospital_id;
    }

    if (filters.doctor_id) {
      where.doctor_id = filters.doctor_id;
    }

    if (filters.hospital_service_id) {
      where.hospital_service_id = filters.hospital_service_id;
    }

    if (filters.is_fast_tag) {
      where.is_fast_tag = filters.is_fast_tag;
    }

    if (filters.created_by) {
      where.created_by = filters.created_by;
    }

    if (filters.appointmentDate) {
      where.appointmentDate = filters.appointmentDate;
    }

    if (filters.from_date && filters.to_date) {
      const fromDate = new Date(filters.from_date);
      fromDate.setHours(0, 0, 0, 0); // Start of the day

      const toDate = new Date(filters.to_date);
      toDate.setHours(23, 59, 59, 999); // End of the day

      where.appointmentDate = Between(fromDate, toDate);
    }
    if (filters.booking_id) {
      where.booking_id = filters.booking_id;
    }
    // console.log("where", where);

    return where;
  }

  async markBookingAsComplete(booking_id: string): Promise<any> {
    // Find the booking by ID
    const booking = await this.bookingRepository.findOne({
      where: { id: booking_id },
    });

    if (!booking) {
      throw new NotFoundException(`Booking with ID ${booking_id} not found`);
    }

    // Check if the booking is already marked as complete
    if (booking.status == BookingStatus.COMPLETED) {
      return {
        status: false,
        message: "Booking is already marked as complete",
      };
    }

    // Mark the booking as complete
    booking.completed_at = new Date();
    booking.status = BookingStatus.COMPLETED;
    await this.bookingRepository.save(booking);

    return {
      status: true,
      message: "Booking marked as complete successfully",
    };
  }

  async bookOfflineHospitalService(
    createBookingDto: CreateServiceBookingDto,
    user_id: string,
  ) {
    const { hospital_id, coupon_id } = createBookingDto;

    const hospital = await this.hospitalService.findById(hospital_id);
    if (!hospital) {
      return this.responseService.errorResponse("Hospital not found");
    }
    const patient = await this.fetchOrCreateUser(createBookingDto.user_details);
    const booking_id = await this.generatebooking_id(hospital_id);

    const booking = this.bookingRepository.create({
      user_id: patient.id,
      status: BookingStatus.ACCEPTED,
      type: BookingType.SERVICE,
      hospital_id,
      booking_id,
      reason: createBookingDto.reason,
      appointmentDate: createBookingDto.appointmentDate,
      timeSlot: createBookingDto.timeSlot,
      hospital_service_id: createBookingDto.hospital_service_id,
      amount: createBookingDto.amount,
      child_user_id: createBookingDto.child_user_id,
      meta_data: createBookingDto.meta_data ?? null,
      created_by: user_id,
      is_online: false, // Set is_online to false
      payment_type: createBookingDto.payment_type ?? "online",
      user_details: createBookingDto.user_details ?? null,
      patient_note: createBookingDto.patient_note ?? null,
    });

    const savedBooking = await this.bookingRepository.save(booking);
    const transaction = await this.createTransaction(savedBooking, coupon_id);
    await this.updateTokenNumber(
      createBookingDto.hospital_service_id,
      createBookingDto.appointmentDate,
      BookingType.SERVICE,
    );

    return this.responseService.successResponse(
      "Offline hospital service booking created successfully",
      { booking: savedBooking },
    );
  }

  async bookOfflineDoctorAppointment(
    createBookingDto: CreateBookingDto,
    user_id: string,
  ) {
    const { doctor_id, hospital_id, amount, coupon_id, appointmentDate } =
      createBookingDto;

    const doctor = await this.doctorService.findById(doctor_id);
    if (!doctor) {
      return this.responseService.errorResponse("Doctor not found");
    }
    // if (is_online === true) {
    let timeSlot = createBookingDto.timeSlot ?? null;
    if (timeSlot) {
      const isSlotAvailable = await this.timeSlotService.checkSlotAvailability(
        doctor_id,
        hospital_id,
        createBookingDto.appointmentDate,
        timeSlot,
      );
      if (!isSlotAvailable) {
        return this.responseService.errorResponse("Time slot is not available");
      }
    } else if (doctor.auto_booking_enabled === true) {
      const slot = await this.timeSlotService.findFirstAvailableSlot(
        doctor,
        appointmentDate,
      );
      timeSlot = slot?.startTime;
      if (!timeSlot) {
        return this.responseService.errorResponse("Time slot is not available");
      }
    }
    // return isSlotAvailable

    // }
    const patient = await this.fetchOrCreateUser(createBookingDto.user_details);
    const booking_id = await this.generatebooking_id(hospital_id);

    const booking = this.bookingRepository.create({
      user_id: patient.id,
      status: BookingStatus.ACCEPTED,
      type: BookingType.CONSULTATION,
      hospital_id,
      doctor_id,
      booking_id,
      reason: createBookingDto.reason,
      appointmentDate: createBookingDto.appointmentDate,
      timeSlot: timeSlot,
      amount,
      child_user_id: createBookingDto.child_user_id,
      meta_data: createBookingDto.meta_data ?? null,
      created_by: user_id,
      is_online: false, // Set is_online to false
      payment_type: createBookingDto.payment_type ?? "online",
      user_details: createBookingDto.user_details ?? null,
      patient_note: createBookingDto.patient_note ?? null,
    });

    const savedBooking = await this.bookingRepository.save(booking);
    const transaction = await this.createTransaction(savedBooking, coupon_id);
    await this.timeSlotService.markSlotAsBooked(
      savedBooking.doctor_id,
      savedBooking.appointmentDate,
      savedBooking.timeSlot,
    );

    await this.updateTokenNumber(
      createBookingDto.doctor_id,
      createBookingDto.appointmentDate,
      BookingType.CONSULTATION,
    );

    return this.responseService.successResponse(
      "Offline doctor appointment booking created successfully",
      { booking: savedBooking },
    );
  }

  async fetchOrCreateUser(userDetail: UserDetail): Promise<User> {
    const { name, phone_number, age, gender } = userDetail;
    let user = await this.userService.findOneByParam({
      phone: phone_number,
      user_type: "user",
    });
    if (!user) {
      user = await this.userService.create({
        role: "user",
        phone: phone_number,
        user_type: "user",
        first_name: name,
        age: age,
        gender: gender == "male" ? Gender.MALE : Gender.FEMALE,
      });
      await this.userService.addRoleToUser(user, "user");
    }
    return user;
  }

  async getLatestCompletedBooking(
    doctor_id: string,
    filters: any,
  ): Promise<any> {
    const fromDate = new Date(filters.appointmentDate);
    fromDate.setHours(0, 0, 0, 0); // Start of the day

    const toDate = new Date(filters.appointmentDate);
    toDate.setHours(23, 59, 59, 999); // End of the day

    const where = {
      doctor_id,
      status: BookingStatus.COMPLETED,
      appointmentDate: Between(fromDate, toDate),
    };

    await this.updateTokenNumber(
      doctor_id,
      filters.appointmentDate,
      BookingType.CONSULTATION,
    );
    // console.log("where", where);
    const booking = await this.bookingRepository.findOne({
      where,
      order: {
        completed_at: "DESC", // Sort by completed_at in descending order
      },
    });

    return this.responseService.successResponse(
      "Latest completed booking fetched successfully",
      booking,
    );
  }

  async getLatestCompletedBookingService(
    hospital_service_id: string,
    filters: any,
  ): Promise<any> {
    const fromDate = new Date(filters.appointmentDate);
    fromDate.setHours(0, 0, 0, 0); // Start of the day

    const toDate = new Date(filters.appointmentDate);
    toDate.setHours(23, 59, 59, 999); // End of the day

    const where = {
      hospital_service_id,
      status: BookingStatus.COMPLETED,
      appointmentDate: Between(fromDate, toDate),
    };

    await this.updateTokenNumber(
      hospital_service_id,
      filters.appointmentDate,
      BookingType.SERVICE,
    );
    // console.log("where", where);
    const booking = await this.bookingRepository.findOne({
      where,
      order: {
        completed_at: "DESC", // Sort by completed_at in descending order
      },
    });

    return this.responseService.successResponse(
      "Latest completed booking fetched successfully",
      booking,
    );
  }
  async updateTokenNumber(id: string, appointmentDate, type: BookingType) {
    console.log("updateTokenNumber");
    try {
      const fromDate = new Date(appointmentDate);
      fromDate.setHours(0, 0, 0, 0); // Start of the day

      const toDate = new Date(appointmentDate);
      toDate.setHours(23, 59, 59, 999); // End of the day

      let where: any = {
        appointmentDate: Between(fromDate, toDate),
      };
      let booking = [];
      if (type == BookingType.CONSULTATION) {
        where["doctor_id"] = id;
        booking = await this.bookingRepository.find({
          where,
          order: {
            timeSlot: "ASC",
          },
        });
      }

      if (type == BookingType.SERVICE) {
        where["hospital_service_id"] = id;
        booking = await this.bookingRepository.find({
          where,
          order: {
            createdAt: "ASC",
          },
        });
      }
      // console.log("where", where);
      console.log("booking", booking.length);

      // Use Promise.all for parallel updates
      await Promise.all(
        booking.map((element, idx) =>
          this.bookingRepository.update(
            { id: element.id },
            { token_number: idx + 1 },
          ),
        ),
      );
    } catch (error) {
      return true;
    }
  }
}
