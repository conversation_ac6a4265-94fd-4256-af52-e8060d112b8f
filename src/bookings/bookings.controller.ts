import {
  Controller,
  Post,
  Get,
  Put,
  Body,
  Req,
  Param,
  UseGuards,
  Query,
  <PERSON>,
  <PERSON><PERSON>,
  Header,
} from "@nestjs/common";

import { CreateBookingDto } from "./dto/create-booking.dto";
import { UpdateBookingDto } from "./dto/update-booking.dto";
import { BookingService } from "./bookings.service";
import { AccessTokenGuard } from "src/common/guards/accessToken.guard";
import { Paginate, PaginateQuery } from "nestjs-paginate";
import { CreateServiceBookingDto } from "./dto/create-service-booking.dto";
import { CreateFastTagBookingDto } from "./dto/create-fasttag-bookings";
import { CalculateAmountDto } from "./dto/calculate-amount-dto";
import { GetBookingDetailsDto } from "./dto/get-bookingCount.dto";
import { ExportService } from "./services/export.service";
import { ExportBookingsDto } from "./dto/export-bookings.dto";
import { Response } from "express";

@Controller("bookings")
// @UseGuards(AccessTokenGuard)
export class BookingController {
  constructor(
    private readonly bookingService: BookingService,
    private readonly exportService: ExportService,
  ) {}

  @UseGuards(AccessTokenGuard)
  @Post()
  async createBooking(
    @Req() req: Request,
    @Body() createBookingDto: CreateBookingDto,
  ) {
    return this.bookingService.createBooking(req["uid"], createBookingDto);
  }

  @UseGuards(AccessTokenGuard)
  @Post("calculate-amount")
  async calculateAmount(
    @Req() req: Request,
    @Body() createBookingDto: CalculateAmountDto,
  ) {
    return this.bookingService.calculateAmount(req["uid"], createBookingDto);
  }

  @UseGuards(AccessTokenGuard)
  @Get("counts")
  async getFastTagBookingDetails(@Query() dto: GetBookingDetailsDto) {
    return this.bookingService.getBookingDetails(dto);
  }

  @UseGuards(AccessTokenGuard)
  @Get("my-bookings")
  async getUserBookings(
    @Req() req: Request,
    @Paginate() query: PaginateQuery,
    @Query() filters: any,
  ) {
    return this.bookingService.getUserBookings(req["uid"], query, filters);
  }

  @UseGuards(AccessTokenGuard)
  @Get("users/:user_id")
  async getUserBookingsById(
    @Param("user_id") user_id: string,
    @Paginate() query: PaginateQuery,
    @Query() filters: any,
  ) {
    return this.bookingService.getUserBookings(user_id, query, filters);
  }

  @UseGuards(AccessTokenGuard)
  @Get("doctor/:doctor_id/bookings")
  async getDoctorBookings(
    @Req() req: Request,
    @Param("doctor_id") doctor_id: string,
    @Paginate() query: PaginateQuery,
    @Query() filters: any,
  ) {
    return this.bookingService.getDoctorBookings(doctor_id, query, filters);
  }

  // @UseGuards(AccessTokenGuard)
  @Get("doctor/:doctor_id/export")
  async exportDoctorBookings(
    @Param("doctor_id") doctor_id: string,
    @Query() filters: any,
    @Res() response: Response,
  ) {
    const { buffer, filename } = await this.exportService.exportDoctorBookings(
      doctor_id,
      filters,
    );

    response.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    );
    response.setHeader(
      "Content-Disposition",
      `attachment; filename=${filename}`,
    );

    response.send(buffer);
  }

  @UseGuards(AccessTokenGuard)
  @Get("hospital/:hospital_id/bookings")
  async getHospitalBookings(
    @Req() req: Request,
    @Param("hospital_id") hospital_id: string,
    @Paginate() query: PaginateQuery,
    @Query() filters: any,
  ) {
    return this.bookingService.getHospitalBookings(hospital_id, query, filters);
  }

  // @UseGuards(AccessTokenGuard)
  @Get("hospital/:hospital_id/export")
  async exportHospitalBookings(
    @Param("hospital_id") hospital_id: string,
    @Query() filters: any,
    @Res() response: Response,
  ) {
    const { buffer, filename } =
      await this.exportService.exportHospitalBookings(hospital_id, filters);

    response.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    );
    response.setHeader(
      "Content-Disposition",
      `attachment; filename=${filename}`,
    );

    response.send(buffer);
  }

  @UseGuards(AccessTokenGuard)
  @Put(":booking_id")
  async updateBooking(
    @Param("booking_id") booking_id: string,
    @Body() updateBookingDto: UpdateBookingDto,
    @Req() req: Request,
  ) {
    return this.bookingService.updateBooking(
      booking_id,
      updateBookingDto,
      req["uid"],
    );
  }

  @UseGuards(AccessTokenGuard)
  @Patch(":booking_id/update-time-slot")
  async updateTimeSlot(
    @Param("booking_id") booking_id: string,
    @Body() updateBookingDto: UpdateBookingDto,
    @Req() req: Request,
  ) {
    return this.bookingService.updateTimeSlot(
      booking_id,
      updateBookingDto,
      req["uid"],
    );
  }
  @UseGuards(AccessTokenGuard)
  @Post("hospital-service")
  async bookHospitalService(
    @Body() updateBookingDto: CreateServiceBookingDto,
    @Req() req: Request,
  ) {
    return this.bookingService.bookHospitalService(
      updateBookingDto,
      req["uid"],
    );
  }

  @UseGuards(AccessTokenGuard)
  @Post("fast-tags")
  async bookFastTag(
    @Body() updateBookingDto: CreateFastTagBookingDto,
    @Req() req: Request,
  ) {
    return this.bookingService.bookFastTag(updateBookingDto, req["uid"]);
  }

  @UseGuards(AccessTokenGuard)
  @Post("offline-hospital-service")
  async bookOfflineHospitalService(
    @Body() createBookingDto: CreateServiceBookingDto,
    @Req() req: Request,
  ) {
    return this.bookingService.bookOfflineHospitalService(
      createBookingDto,
      req["uid"],
    );
  }

  @UseGuards(AccessTokenGuard)
  @Post("offline-doctor-appointment")
  async bookOfflineDoctorAppointment(
    @Body() createBookingDto: CreateBookingDto,
    @Req() req: Request,
  ) {
    return this.bookingService.bookOfflineDoctorAppointment(
      createBookingDto,
      req["uid"],
    );
  }

  @UseGuards(AccessTokenGuard)
  @Patch(":booking_id/mark-complete")
  async markBookingAsComplete(@Param("booking_id") booking_id: string) {
    return this.bookingService.markBookingAsComplete(booking_id);
  }

  @UseGuards(AccessTokenGuard)
  @Get("doctor/:doctor_id/latest-completed")
  async getLatestCompletedBooking(
    @Param("doctor_id") doctor_id: string,
    @Query() filters: any,
  ) {
    if (!filters.appointmentDate) {
      throw new Error("appointmentDate is required");
    }
    return this.bookingService.getLatestCompletedBooking(doctor_id, filters);
  }

  @UseGuards(AccessTokenGuard)
  @Get("services/:service_id/latest-completed")
  async getLatestCompletedBookingService(
    @Param("service_id") service_id: string,
    @Query() filters: any,
  ) {
    if (!filters.appointmentDate) {
      throw new Error("appointmentDate is required");
    }
    return this.bookingService.getLatestCompletedBookingService(
      service_id,
      filters,
    );
  }
}
