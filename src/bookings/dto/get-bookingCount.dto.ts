import { IsString, <PERSON>NotEmpty, <PERSON><PERSON>ption<PERSON>, IsDate } from "class-validator";
import { Type } from "class-transformer";

export class GetBookingDetailsDto {
  @IsNotEmpty()
  @IsString()
  user_id: string;

  @IsNotEmpty()
  @IsString()
  hospital_id: string;

  @IsOptional()
  @IsString()
  hospital_service_id?: string;

  @IsNotEmpty()
  @IsDate()
  @Type(() => Date) // Transform input to a Date object
  date: Date;
}
