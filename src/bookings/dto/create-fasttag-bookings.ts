import {
  IsNotEmpty,
  IsDate,
  IsString,
  IsNumber,
  IsBoolean,
  ValidateIf,
  IsObject,
  IsOptional,
} from "class-validator";
import { UserDetail } from "../entities/booking.entity";

export class CreateFastTagBookingDto {
  @IsNotEmpty()
  appointmentDate: Date;

  @ValidateIf((o) => o.is_online === false)
  @IsNotEmpty()
  hospital_id: string;

  @ValidateIf((o) => o.is_online === false)
  @IsOptional()
  child_user_id: string;

  @IsObject()
  @IsOptional()
  user_details?: UserDetail;

  @IsObject()
  @IsOptional()
  meta_data?: any;

  @IsOptional()
  coupon_id: string;

  @IsNotEmpty()
  amount: number;
}
