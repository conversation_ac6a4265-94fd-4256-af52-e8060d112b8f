import {
  IsNotEmpty,
  IsDate,
  IsString,
  IsNumber,
  IsBoolean,
  ValidateIf,
  IsObject,
  IsOptional,
} from "class-validator";
import { UserDetail } from "../entities/booking.entity";

export class CreateBookingDto {
  @IsNotEmpty()
  doctor_id: string;

  @IsOptional()
  user_id: string;

  @IsNotEmpty()
  @IsBoolean()
  is_online: boolean;

  @IsOptional()
  @IsString()
  reason: string;

  @IsNotEmpty()
  appointmentDate: Date;

  @ValidateIf((o) => o.is_online === false)
  @IsNotEmpty()
  hospital_id: string;

  @ValidateIf((o) => o.is_online === false)
  @IsOptional()
  child_user_id: string;

  @ValidateIf((o) => o.is_online === true)
  @IsNotEmpty()
  @IsString()
  timeSlot: string;

  @IsObject()
  @IsOptional()
  user_details?: UserDetail;

  @IsObject()
  @IsOptional()
  meta_data?: any;

  @IsOptional()
  coupon_id: string;

  @IsNotEmpty()
  amount: number;

  @IsOptional()
  is_service: boolean;

  @IsOptional()
  payment_type: string;

  @IsOptional()
  patient_note: string;
}
