import { IsEnum, IsOptional, IsDateString, IsBoolean } from "class-validator";
import { BookingStatus } from "../entities/booking.entity";

export class ExportBookingsDto {
  @IsOptional()
  @IsEnum(BookingStatus)
  status?: BookingStatus;

  @IsOptional()
  @IsDateString()
  from_date?: string;

  @IsOptional()
  @IsDateString()
  to_date?: string;

  @IsOptional()
  @IsBoolean()
  is_online?: boolean;
}
