import {
  IsNotEmpty,
  IsDate,
  IsString,
  IsNumber,
  IsBoolean,
  ValidateIf,
  IsObject,
  IsOptional,
  IsEnum,
} from "class-validator";
import { UserDetail } from "../entities/booking.entity";

export class CreateServiceBookingDto {
  @IsNotEmpty()
  appointmentDate: Date;

  @IsNotEmpty()
  hospital_id: string;

  @IsNotEmpty()
  hospital_service_id: string;

  @IsOptional()
  child_user_id: string;

  @IsOptional()
  @IsString()
  timeSlot: string;

  @IsObject()
  @IsOptional()
  user_details?: UserDetail;

  @IsObject()
  @IsOptional()
  meta_data?: any;

  @IsNotEmpty()
  amount: number;

  @IsOptional()
  coupon_id: string;

  @IsOptional()
  is_service: boolean;

  @IsOptional()
  @IsString()
  reason: string;

  @IsOptional()
  payment_type: string;

  @IsOptional()
  patient_note: string;
}
