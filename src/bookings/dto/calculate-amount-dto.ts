import {
  IsBoolean,
  IsNot<PERSON>mpty,
  Is<PERSON><PERSON>al,
  Is<PERSON><PERSON><PERSON>,
  ValidateIf,
} from "class-validator";

export class CalculateAmountDto {
  @IsBoolean()
  @IsNotEmpty()
  is_fast_tag: boolean;

  @ValidateIf((o) => o.is_fast_tag === true)
  @IsUUID()
  @IsNotEmpty({ message: "hospital_id is required when is_fast_tag is true" })
  hospital_id?: string;

  @ValidateIf((o) => o.is_fast_tag === false)
  @IsUUID()
  @IsOptional()
  doctor_id?: string;

  @ValidateIf((o) => o.is_fast_tag === false)
  @IsUUID()
  @IsOptional()
  hospital_service_id?: string;

  @IsOptional()
  coupon_id?: string;
}
