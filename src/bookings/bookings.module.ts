import { forwardRef, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { BookingController } from "./bookings.controller";
import { BookingService } from "./bookings.service";
import { Booking } from "./entities/booking.entity";
import { DoctorsModule } from "src/doctors/doctors.module";
import { HospitalsModule } from "src/hospitals/hospitals.module";
import { TimeSlotModule } from "src/time-slots/time-slots.module";
import { TransactionsModule } from "src/transactions/transactions.module";
import { CashfreeModule } from "src/cashfree/cashfree.module";
import { UsersModule } from "src/users/users.module";
import { HospitalServicesModule } from "src/hospital-services/hospital-services.module";
import { CouponsModule } from "src/coupons/coupons.module";
import { ExportService } from "./services/export.service";
import { CommonModule } from "src/common/common.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([Booking]),
    DoctorsModule,
    HospitalsModule,
    TimeSlotModule,
    forwardRef(() => TransactionsModule),
    CashfreeModule,
    UsersModule,
    HospitalServicesModule,
    CouponsModule,
    CommonModule,
  ],
  controllers: [BookingController],
  providers: [BookingService, ExportService],
  exports: [BookingService],
})
export class BookingModule {}
