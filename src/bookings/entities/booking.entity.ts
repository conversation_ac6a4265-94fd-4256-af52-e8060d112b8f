import { ChildUser } from "src/child_users/entities/child_user.entity";
import { BookingType } from "src/common/enums/status.enum";
import { Doctor } from "src/doctors/entities/doctor.entity";
import { HospitalService } from "src/hospital-services/entities/hospital-service.entity";
import { Hospital } from "src/hospitals/entities/hospital.entity";
import { Transaction } from "src/transactions/entities/transaction.entity";
import { User } from "src/users/entities/user.entity";
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
  OneToMany,
} from "typeorm";

export enum BookingStatus {
  STARTED = "started",
  PENDING = "pending",
  ACCEPTED = "accepted",
  REJECTED = "rejected",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
}

export interface UserDetail {
  name: string;
  gender: string;
  age: number;
  phone_number?: string;
  address?: string;
}

@Entity("bookings")
export class Booking {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ unique: true })
  booking_id: string;

  @Column({ type: "simple-json", nullable: true })
  meta_data: Record<string, any> | null;

  @Column({ type: "varchar", length: 36, nullable: true })
  created_by: string | null;

  @ManyToOne(() => User, { nullable: true, onDelete: "SET NULL" })
  @JoinColumn({ name: "created_by" })
  creator: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: "user_id" })
  user: User;

  @ManyToOne(() => Doctor)
  @JoinColumn({ name: "doctor_id" })
  doctor: Doctor;

  @ManyToOne(() => Hospital)
  @JoinColumn({ name: "hospital_id" })
  hospital: Hospital;

  @ManyToOne(() => HospitalService)
  @JoinColumn({ name: "hospital_service_id" })
  hospitalService: HospitalService;

  @Column({ type: "datetime" })
  appointmentDate: Date;

  @Column({ type: "datetime" })
  completed_at?: Date;

  @Column({ type: "time" })
  timeSlot: string;

  @Column()
  hospital_id: string;

  @Column()
  doctor_id: string;

  @Column()
  hospital_service_id: string;

  @Column()
  patient_note: string;

  @Column()
  user_id: string;

  @Column()
  child_user_id: string;

  @Column("simple-json", { nullable: true })
  user_details: UserDetail;

  @ManyToOne(() => ChildUser)
  @JoinColumn({ name: "child_user_id" })
  childUser: ChildUser;

  @Column({
    type: "enum",
    enum: BookingStatus,
    default: BookingStatus.STARTED,
  })
  status: BookingStatus;

  @Column({ nullable: true })
  prescriptionUrl: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({
    type: "enum",
    enum: BookingType,
    default: BookingType.CONSULTATION,
  })
  type: BookingType;

  @Column({ type: "boolean", default: false })
  is_fast_tag: boolean;

  @Column({ type: "varchar" })
  reason?: string;

  @Column({ type: "decimal", precision: 10, scale: 2, nullable: true })
  amount: number;

  @Column({ type: "boolean", default: true, nullable: true })
  is_online: boolean | null;

  @Column({ type: "int", nullable: true })
  token_number?: number | null;

  @Column({ type: "varchar", length: 255, nullable: true })
  payment_type: string;

  @OneToMany(() => Transaction, (review) => review.booking)
  transactions: Transaction[];
}
