import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Between, Repository } from "typeorm";
import { Booking, BookingStatus } from "../entities/booking.entity";
import { ExportBookingsDto } from "../dto/export-bookings.dto";
import * as ExcelJS from "exceljs";
import { Response } from "express";
import { BookingService } from "../bookings.service";

@Injectable()
export class ExportService {
  constructor(
    @InjectRepository(Booking)
    private bookingRepository: Repository<Booking>,
    private bookingService: BookingService,
  ) {}

  // private buildWhereFilters(filters: ExportBookingsDto): any {
  //   const where: any = {};

  //   if (filters.status) {
  //     where.status = filters.status;
  //   }

  //   if (filters.from_date && filters.to_date) {
  //     const fromDate = new Date(filters.from_date);
  //     fromDate.setHours(0, 0, 0, 0);

  //     const toDate = new Date(filters.to_date);
  //     toDate.setHours(23, 59, 59, 999);

  //     where.appointmentDate = Between(fromDate, toDate);
  //   }

  //   if (filters.is_online !== undefined) {
  //     where.is_online = filters.is_online;
  //   }

  //   return where;
  // }

  private async createExcelWorkbook(data: any[], type: "hospital" | "doctor") {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Bookings");

    // Define columns based on booking type
    const commonColumns = [
      { header: "Booking ID", key: "booking_id", width: 15 },
      { header: "Patient Name", key: "patient_name", width: 20 },
      { header: "Age", key: "patient_age", width: 10 },
      { header: "Gender", key: "patient_gender", width: 10 },
      { header: "Appointment Date", key: "appointment_date", width: 15 },
      { header: "Time Slot", key: "time_slot", width: 10 },
      { header: "Status", key: "status", width: 15 },
      { header: "Amount", key: "amount", width: 12 },
      { header: "Mode", key: "is_online", width: 10 },
      { header: "Type", key: "booking_type", width: 15 },
      { header: "Completed At", key: "completed_at", width: 20 },
      { header: "Reason", key: "reason", width: 25 },
    ];

    const specificColumns =
      type === "hospital"
        ? [
            { header: "Doctor Name", key: "doctor_name", width: 20 },
            { header: "Service Name", key: "service_name", width: 20 },
          ]
        : [{ header: "Hospital Name", key: "hospital_name", width: 20 }];

    worksheet.columns = [...commonColumns, ...specificColumns];

    // Add data and format cells
    data.forEach((row) => {
      const formattedRow = {
        ...row,
        appointment_date: this.formatDate(row.appointment_date),
        completed_at:
          row.completed_at !== "N/A"
            ? this.formatDate(row.completed_at)
            : "N/A",
        amount: Number(row.amount),
      };
      worksheet.addRow(formattedRow);
    });

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFE0E0E0" },
    };

    // Format amount column
    const amountColumn = worksheet.getColumn("amount");
    amountColumn.numFmt = "₹#,##0.00";

    return workbook;
  }

  private formatDate(date: Date | string): string {
    if (!date || date === "N/A") return "N/A";
    return new Date(date).toLocaleString("en-IN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  async exportHospitalBookings(
    hospital_id: string,
    filters: ExportBookingsDto,
  ) {
    const where = await this.bookingService.buildWhereFilters(filters);
    where.hospital_id = hospital_id;

    const bookings = await this.bookingRepository.find({
      where,
      relations: ["doctor", "hospitalService"],
      order: {
        appointmentDate: "DESC",
        timeSlot: "ASC",
      },
    });

    const formattedData = bookings.map((booking) => ({
      booking_id: booking.booking_id,
      patient_name: booking.user_details?.name || "N/A",
      patient_age: booking.user_details?.age || "N/A",
      patient_gender: booking.user_details?.gender || "N/A",
      appointment_date: booking.appointmentDate,
      time_slot: booking.timeSlot,
      status: booking.status,
      amount: booking.amount,
      is_online: booking.is_online ? "Online" : "Offline",
      booking_type: booking.type,
      completed_at: booking.completed_at || "N/A",
      reason: booking.reason || "N/A",
      doctor_name: booking.doctor?.name || "N/A",
      service_name: booking.hospitalService?.name || "N/A",
    }));

    const workbook = await this.createExcelWorkbook(formattedData, "hospital");
    const buffer = await workbook.xlsx.writeBuffer();
    return {
      buffer,
      filename: `hospital_bookings_${hospital_id}.xlsx`,
    };
  }

  async exportDoctorBookings(doctor_id: string, filters: ExportBookingsDto) {
    const where = await this.bookingService.buildWhereFilters(filters);
    where.doctor_id = doctor_id;

    const bookings = await this.bookingRepository.find({
      where,
      relations: ["hospital"],
      order: {
        appointmentDate: "DESC",
        timeSlot: "ASC",
      },
    });

    const formattedData = bookings.map((booking) => ({
      booking_id: booking.booking_id,
      patient_name: booking.user_details?.name || "N/A",
      patient_age: booking.user_details?.age || "N/A",
      patient_gender: booking.user_details?.gender || "N/A",
      appointment_date: booking.appointmentDate,
      time_slot: booking.timeSlot,
      status: booking.status,
      amount: booking.amount,
      is_online: booking.is_online ? "Online" : "Offline",
      booking_type: booking.type,
      completed_at: booking.completed_at || "N/A",
      reason: booking.reason || "N/A",
      hospital_name: booking.hospital?.name || "N/A",
    }));

    const workbook = await this.createExcelWorkbook(formattedData, "doctor");
    const buffer = await workbook.xlsx.writeBuffer();
    return {
      buffer,
      filename: `doctor_bookings_${doctor_id}.xlsx`,
    };
  }
}
